# 雪球评论数据爬取指南

## 🎯 功能概述

本功能专门用于从雪球个股详情页面的HTML中提取评论数据，确保提取出的数据结构与雪球API接口 `https://xueqiu.com/query/v1/symbol/search/status.json` 返回的JSON格式完全一致。

## 🔧 核心特性

### ✨ 数据结构一致性
- **API兼容**: 提取的数据结构与雪球官方API完全一致
- **字段完整**: 包含所有API字段：`id`, `text`, `user`, `created_at`, `fav_count`, `reply_count`
- **类型正确**: 确保数据类型与API响应一致

### 🛡️ 反爬虫技术
- **智能HTML解析**: 从JavaScript变量和DOM结构双重提取
- **反检测机制**: 集成完整的反爬虫检测系统
- **页面交互**: 自动滚动和点击加载更多内容

### 📊 数据提取策略
- **多层提取**: JavaScript变量 → DOM结构 → 备用选择器
- **智能识别**: 自动识别评论容器和项目
- **数据验证**: 确保提取数据的完整性和准确性

## 📋 API数据结构

### 标准评论对象
```json
{
  "id": "评论ID",
  "text": "评论内容",
  "user": {
    "screen_name": "用户名",
    "id": "用户ID"
  },
  "created_at": "发布时间",
  "fav_count": 点赞数,
  "reply_count": 回复数
}
```

### 完整API响应格式
```json
{
  "count": 10,
  "maxPage": 1,
  "page": 1,
  "list": [
    {
      "id": "123456789",
      "text": "这只股票很有潜力，值得关注...",
      "user": {
        "screen_name": "投资者A",
        "id": "987654321"
      },
      "created_at": "2024-01-15T10:30:00",
      "fav_count": 12,
      "reply_count": 3
    }
  ]
}
```

## 🚀 使用方法

### 1. 基础使用
```python
from crawler_engine import XueqiuCrawler
import asyncio

async def basic_example():
    crawler = XueqiuCrawler(enable_anti_detection=True)
    
    # 爬取评论数据
    result = await crawler.crawl_stock_comments("SH688775", count=10)
    
    if result.success:
        comments = result.data  # 已经是API格式的数据
        print(f"成功爬取 {len(comments)} 条评论")
        
        # 数据格式与API完全一致
        for comment in comments:
            print(f"作者: {comment['user']['screen_name']}")
            print(f"内容: {comment['text']}")
            print(f"点赞: {comment['fav_count']}")

asyncio.run(basic_example())
```

### 2. 命令行使用
```bash
# 爬取单个股票评论
python main_advanced.py --mode comments --symbols SH688775 --comment-count 20

# 批量爬取多个股票评论
python main_advanced.py --mode comments --symbols SH688775 SZ000001 SH600036

# 保存为JSON文件
python main_advanced.py --mode comments --symbols SH688775 --output comments_result.json
```

### 3. 高级使用
```python
from crawler_engine import XueqiuCrawler
import json

async def advanced_example():
    crawler = XueqiuCrawler(enable_anti_detection=True)
    
    # 批量爬取多个股票
    symbols = ["SH688775", "SZ000001", "SH600036"]
    all_results = {}
    
    for symbol in symbols:
        result = await crawler.crawl_stock_comments(symbol, count=15)
        
        if result.success:
            # 构造完整的API响应格式
            api_response = {
                "count": len(result.data),
                "maxPage": 1,
                "page": 1,
                "list": result.data
            }
            all_results[symbol] = api_response
    
    # 保存结果
    with open('comments_batch.json', 'w', encoding='utf-8') as f:
        json.dump(all_results, f, ensure_ascii=False, indent=2)
```

## 🧪 测试和验证

### 运行测试套件
```bash
# 运行完整测试
python test_comments_crawler.py

# 运行使用示例
python example_comments_usage.py
```

### 数据结构验证
测试脚本会自动验证以下内容：
- ✅ 必需字段完整性
- ✅ 数据类型正确性
- ✅ API格式兼容性
- ✅ 用户信息结构

## 📊 输出示例

### 成功响应示例
```json
{
  "success": true,
  "symbol": "SH688775",
  "data_type": "comments",
  "total_comments": 10,
  "response_time": 3.45,
  "api_format": {
    "count": 10,
    "maxPage": 1,
    "page": 1,
    "list": [
      {
        "id": "123456789",
        "text": "影石创新的产品很有创新性，看好未来发展",
        "user": {
          "screen_name": "科技投资者",
          "id": "987654321"
        },
        "created_at": "2024-01-15T14:30:00",
        "fav_count": 8,
        "reply_count": 2
      }
    ]
  }
}
```

## ⚙️ 配置说明

### 选择器配置
系统使用多层选择器策略，针对雪球页面优化：

```python
# 评论容器选择器
"container": [
    ".timeline",           # 雪球时间线
    ".status-list",        # 状态列表
    ".feed-list",          # 动态列表
    "[class*='timeline']", # 包含timeline的类
]

# 评论项目选择器
"item": [
    ".status-item",        # 状态项目
    ".timeline-item",      # 时间线项目
    "[data-id]",          # 有ID的元素
]
```

### 反爬虫配置
```python
# 页面交互配置
"crawl_settings": {
    "wait_for": "networkidle",
    "page_timeout": 30000,
    "scroll_to_load_comments": True,
    "delay_between_requests": 3
}
```

## 🔍 故障排除

### 常见问题

1. **评论数量少于预期**
   - 检查页面是否完全加载
   - 尝试增加页面等待时间
   - 确认股票是否有足够的讨论

2. **数据结构不完整**
   - 检查选择器配置
   - 查看页面HTML结构变化
   - 启用调试日志查看详细信息

3. **爬取失败**
   - 检查网络连接
   - 验证反爬虫设置
   - 降低请求频率

### 调试模式
```bash
# 启用详细日志
export PYTHONPATH=.
python -c "
import logging
logging.basicConfig(level=logging.DEBUG)
from test_comments_crawler import CommentsTestSuite
import asyncio
test = CommentsTestSuite()
asyncio.run(test.test_single_stock_comments('SH688775'))
"
```

## 📈 性能优化

### 建议配置
- **并发数**: 建议不超过3个并发请求
- **请求间隔**: 每次请求间隔2-5秒
- **页面超时**: 设置30秒超时时间
- **重试机制**: 启用智能重试

### 最佳实践
1. 使用反检测功能避免被封
2. 合理控制爬取频率
3. 定期更新选择器配置
4. 监控成功率和响应时间

## 📝 更新日志

### v2.1.0 (当前版本)
- ✅ 新增HTML评论提取功能
- ✅ 确保API数据结构一致性
- ✅ 优化JavaScript变量提取
- ✅ 扩展DOM选择器策略
- ✅ 添加数据验证机制
- ✅ 完善测试套件

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进评论爬取功能！

## ⚠️ 免责声明

本功能仅供学习和研究使用，请遵守相关法律法规和网站使用条款。

# 📁 aicrawl 项目结构说明

## 🎯 项目概述

aicrawl是一个专门针对雪球网站的智能爬虫系统，具备完整的反爬虫机制和数据提取能力。核心功能是从HTML页面提取评论数据，确保数据格式与雪球API完全一致。

## 📂 目录结构

```
core/aicrawl/
├── main_advanced.py               # 🚀 主程序入口
├── crawler_engine.py              # 🔧 爬虫引擎核心
├── extractors.py                  # 📊 数据提取器
├── anti_detection.py              # 🛡️ 反检测模块
├── config.py                      # ⚙️ 配置文件
├── requirements.txt               # 📦 依赖包列表
├── README.md                      # 📖 项目说明
├── FINAL_USAGE_GUIDE.md          # 📋 使用指南
├── PROJECT_STRUCTURE.md          # 📁 本文件
└── docs/                          # 📚 详细文档目录
    ├── COMMENTS_CRAWLING_GUIDE.md # 评论爬取详细指南
    ├── ENHANCED_FEATURES.md       # 增强功能说明
    ├── HTML_CRAWLING_GUIDE.md     # HTML爬取技术文档
    ├── IMPLEMENTATION_SUMMARY.md  # 实现总结
    ├── PROJECT_SUMMARY.md         # 项目总结
    └── UPGRADE_SUMMARY.md         # 升级历程记录
```

## 🔧 核心文件说明

### 主程序模块

#### `main_advanced.py` - 主程序入口
- **功能**: 命令行接口，支持多种爬取模式
- **用法**: `python main_advanced.py --mode comments --symbols SH688775`
- **特性**: 
  - 支持首页、详情、评论三种爬取模式
  - 批量处理多个股票
  - 结果保存为JSON格式
  - 完整的错误处理和日志记录

#### `crawler_engine.py` - 爬虫引擎
- **功能**: 核心爬虫逻辑，包含SmartCrawler和XueqiuCrawler
- **特性**:
  - 智能反爬虫检测
  - JavaScript增强执行
  - 动态页面内容处理
  - 并发爬取支持
  - 性能监控和自适应调整

#### `extractors.py` - 数据提取器
- **功能**: 从HTML页面提取结构化数据
- **支持数据类型**:
  - 股票基本信息
  - 首页热门内容
  - 用户评论（与API格式一致）
- **特性**:
  - 多层提取策略（JavaScript变量 + DOM解析）
  - 智能数据验证和清洗
  - 容错处理机制

#### `anti_detection.py` - 反检测模块
- **功能**: 反爬虫检测和规避
- **技术**:
  - User-Agent池轮换
  - 请求头随机化
  - 访问频率控制
  - IP代理支持（可选）
  - 浏览器指纹伪装

#### `config.py` - 配置管理
- **功能**: 集中管理所有配置参数
- **包含**:
  - 反爬虫配置（User-Agent池、请求间隔等）
  - 页面选择器配置
  - URL模板和API端点
  - 爬取参数设置

### 依赖和文档

#### `requirements.txt` - 依赖包
```
crawl4ai>=0.7.4
beautifulsoup4>=4.12.0
aiohttp>=3.8.0
playwright>=1.40.0
```

#### `README.md` - 项目说明
- 项目介绍和特性
- 快速开始指南
- 基础使用方法
- 环境搭建说明

#### `FINAL_USAGE_GUIDE.md` - 详细使用指南
- 完整的使用说明
- 实际运行示例
- 数据格式说明
- 故障排除指南

## 📚 文档目录说明

### `docs/` 目录包含详细的技术文档：

- **COMMENTS_CRAWLING_GUIDE.md**: 评论爬取功能的详细技术指南
- **ENHANCED_FEATURES.md**: 系统增强功能的详细说明
- **HTML_CRAWLING_GUIDE.md**: HTML页面爬取技术文档
- **IMPLEMENTATION_SUMMARY.md**: 项目实现的技术总结
- **PROJECT_SUMMARY.md**: 项目整体总结和特性说明
- **UPGRADE_SUMMARY.md**: 系统升级和改进历程

## 🚀 快速开始

### 1. 环境准备
```bash
# 安装依赖
pip install -r requirements.txt

# 安装浏览器
playwright install chromium
```

### 2. 基础使用
```bash
# 爬取评论（核心功能）
python main_advanced.py --mode comments --symbols SH688775 --comment-count 10

# 爬取首页
python main_advanced.py --mode homepage

# 爬取股票详情
python main_advanced.py --mode detail --symbols SH688775
```

### 3. 高级用法
```bash
# 批量爬取多个股票的评论
python main_advanced.py --mode comments --symbols SH688775 SZ000001 SH600036

# 保存结果到文件
python main_advanced.py --mode comments --symbols SH688775 --output result.json
```

## 💡 核心特性

- ✅ **评论数据提取**: 从HTML页面提取评论，格式与API完全一致
- ✅ **反爬虫机制**: 完整的反检测系统，稳定可靠
- ✅ **批量处理**: 支持多股票并发爬取
- ✅ **数据验证**: 自动验证和标准化数据格式
- ✅ **错误处理**: 完善的异常处理和恢复机制
- ✅ **性能优化**: 智能频率控制和资源管理

## 🔍 技术亮点

1. **JavaScript增强执行**: 处理动态加载的页面内容
2. **多层数据提取**: JavaScript变量 + DOM结构双重保障
3. **API格式兼容**: 输出数据与雪球官方API完全一致
4. **智能过滤**: 自动排除导航元素和无效内容
5. **模块化设计**: 清晰的代码结构，易于维护和扩展

## 📊 项目状态

- **开发状态**: ✅ 完成
- **核心功能**: ✅ 评论爬取完全实现
- **测试状态**: ✅ 全面测试通过
- **生产就绪**: ✅ 可直接投入使用
- **文档完整性**: ✅ 完整的使用和技术文档

## 🎯 使用建议

1. **新用户**: 先阅读 `FINAL_USAGE_GUIDE.md` 了解基础用法
2. **开发者**: 查看 `docs/` 目录下的技术文档
3. **问题排查**: 参考使用指南中的故障排除部分
4. **功能扩展**: 基于现有模块化架构进行开发

项目结构清晰，文档完整，可直接投入生产使用！🚀

# 雪球全站自动爬取系统使用指南

## 🎯 系统概述

雪球全站自动爬取系统是一个专业的自动化爬虫系统，专门用于爬取雪球网站的热门自媒体创作内容和个股评论。系统具有以下特点：

- **全自动运行**：无需手动干预，按配置自动执行爬取任务
- **智能调度**：支持多种爬取策略和时间间隔配置
- **数据去重**：自动去除重复数据，确保数据质量
- **监控报警**：实时监控系统状态和性能指标
- **API接口**：提供完整的Web API用于管理和监控
- **反爬虫**：集成先进的反检测技术，稳定可靠

## 🏗️ 系统架构

```
雪球自动爬取系统
├── 🤖 auto_crawler.py        # 核心调度器
├── 💾 data_storage.py        # 数据存储管理
├── 📊 monitoring.py          # 监控和指标
├── 🌐 api_server.py          # Web API服务
├── 🚀 start_auto_crawler.py  # 启动脚本
├── ⚙️  config.py             # 配置管理
└── 📋 crawler_config.json    # 配置文件
```

## 🚀 快速开始

### 1. 安装依赖

```bash
cd core/aicrawl
pip install -r requirements.txt

# 额外依赖
pip install fastapi uvicorn schedule aiosqlite
```

### 2. 创建配置文件

```bash
python start_auto_crawler.py --create-config
```

这将创建 `crawler_config.json` 配置文件，您可以根据需要修改配置。

### 3. 启动系统

#### 方式一：直接运行模式
```bash
python start_auto_crawler.py --mode direct
```

#### 方式二：API服务器模式
```bash
python start_auto_crawler.py --mode api --host 0.0.0.0 --port 8000
```

#### 方式三：守护进程模式
```bash
python start_auto_crawler.py --mode daemon
```

## ⚙️ 配置说明

### 主要配置项

```json
{
  "homepage_interval": 30,           // 首页爬取间隔（分钟）
  "stock_comments_interval": 60,     // 个股评论爬取间隔（分钟）
  "trending_stocks_interval": 15,    // 热门股票发现间隔（分钟）
  "max_concurrent_tasks": 3,         // 最大并发任务数
  "max_comments_per_stock": 30,      // 每个股票最多爬取评论数
  "max_trending_stocks": 50,         // 最多跟踪热门股票数
  "max_homepage_posts": 200,         // 首页最多爬取动态数
  "data_retention_days": 30,         // 数据保留天数
  "enable_anti_detection": true      // 启用反检测功能
}
```

### 爬取策略配置

- **conservative（保守）**：较慢但更稳定，适合长期运行
- **moderate（适中）**：平衡速度和稳定性，推荐使用
- **aggressive（激进）**：更快但可能被检测，谨慎使用

## 🌐 API接口

系统提供完整的RESTful API接口：

### 系统管理
- `POST /crawler/start` - 启动爬虫系统
- `POST /crawler/stop` - 停止爬虫系统
- `GET /crawler/status` - 获取系统状态
- `GET /health` - 健康检查

### 任务管理
- `GET /crawler/tasks` - 获取任务列表
- `POST /crawler/task/manual` - 创建手动任务

### 数据统计
- `GET /data/statistics` - 获取数据统计
- `POST /data/cleanup` - 清理过期数据

### 监控指标
- `GET /monitoring/metrics` - 获取性能指标
- `GET /monitoring/activity` - 获取最近活动
- `GET /monitoring/export` - 导出监控数据

### API使用示例

```bash
# 启动爬虫系统
curl -X POST "http://localhost:8000/crawler/start" \
  -H "Content-Type: application/json" \
  -d '{
    "homepage_interval": 30,
    "stock_comments_interval": 60,
    "max_concurrent_tasks": 3
  }'

# 获取系统状态
curl "http://localhost:8000/crawler/status"

# 创建手动任务
curl -X POST "http://localhost:8000/crawler/task/manual" \
  -H "Content-Type: application/json" \
  -d '{
    "task_type": "stock_comments",
    "target": "SH688627",
    "priority": 8
  }'
```

## 📊 数据结构

### 爬取的数据类型

1. **首页热门内容**
   - 热门股票信息
   - 热门话题讨论
   - 用户动态内容

2. **个股评论数据**
   - 评论内容和作者
   - 点赞和回复数量
   - 情感分析结果
   - 提到的股票代码

3. **热门股票列表**
   - 实时热门股票排行
   - 股票基本信息
   - 市场表现数据

### 数据存储

系统使用SQLite数据库存储所有爬取的数据：

- `hot_stocks` - 热门股票表
- `hot_topics` - 热门话题表
- `user_posts` - 用户动态表
- `stock_comments` - 股票评论表
- `trending_stocks` - 热门股票列表表
- `crawl_results` - 爬取结果表

## 🔍 监控和维护

### 系统状态检查

```bash
# 查看系统状态
python start_auto_crawler.py --status

# 查看日志
tail -f auto_crawler.log
```

### 性能监控

系统提供详细的性能监控指标：

- 任务成功率和失败率
- 平均响应时间
- 每小时任务数量
- 错误统计和趋势
- 数据量统计

### 数据维护

```bash
# 清理30天前的数据
curl -X POST "http://localhost:8000/data/cleanup?retention_days=30"

# 导出监控数据
curl "http://localhost:8000/monitoring/export" -o metrics.json
```

## 🛠️ 故障排除

### 常见问题

1. **爬取失败率高**
   - 检查网络连接
   - 降低并发数量
   - 增加延迟时间
   - 检查反检测配置

2. **数据重复**
   - 系统自动去重，无需担心
   - 可通过数据哈希值验证

3. **内存占用过高**
   - 减少数据保留天数
   - 降低最大爬取数量
   - 定期清理过期数据

4. **API服务无响应**
   - 检查端口是否被占用
   - 查看错误日志
   - 重启API服务

### 日志文件

- `auto_crawler.log` - 主系统日志
- `auto_crawler_startup.log` - 启动日志
- `crawler.log` - 爬虫引擎日志

## 🔧 高级配置

### 自定义爬取策略

您可以修改 `config.py` 中的滚动策略：

```python
SCROLL_STRATEGIES = {
    "custom": {
        "max_scrolls": 5,
        "scroll_delay": 2000,
        "step_delay": 1000,
        "positions": [0.3, 0.6, 0.9, 1.0]
    }
}
```

### 数据库配置

默认使用SQLite，可以修改为其他数据库：

```python
# 在 data_storage.py 中修改
class DataStorage:
    def __init__(self, db_path: str = "custom_path.db"):
        # 自定义数据库路径
```

### 反爬虫配置

在 `config.py` 中调整反检测参数：

```python
REQUEST_DELAYS = {
    "min_delay": 3,      # 最小延迟（秒）
    "max_delay": 8,      # 最大延迟（秒）
    "error_delay": 10    # 错误后延迟（秒）
}
```

## 📈 最佳实践

1. **合理设置爬取间隔**：避免过于频繁的请求
2. **监控系统性能**：定期检查成功率和响应时间
3. **及时清理数据**：避免数据库过大影响性能
4. **备份重要数据**：定期备份数据库文件
5. **更新反检测策略**：根据网站变化调整配置

## 🆘 技术支持

如遇到问题，请：

1. 查看日志文件获取详细错误信息
2. 检查配置文件是否正确
3. 确认网络连接和目标网站可访问性
4. 参考本文档的故障排除部分

---

**注意**：请遵守网站的robots.txt和使用条款，合理使用爬虫功能，避免对目标网站造成过大负担。

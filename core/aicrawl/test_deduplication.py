#!/usr/bin/env python3
"""
测试去重功能
验证评论数据是否存在重复
"""
import asyncio
import json
import logging
from collections import Counter
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def analyze_duplicates_in_file(filename: str):
    """分析文件中的重复数据"""
    print(f"🔍 分析文件: {filename}")
    print("=" * 50)
    
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        if not isinstance(data, list) or not data:
            print("❌ 文件格式不正确或为空")
            return
        
        result = data[0]  # 假设只有一个结果
        api_format = result.get('api_format', {})
        comments = api_format.get('list', [])
        
        print(f"📊 总评论数: {len(comments)}")
        
        # 分析文本重复
        texts = [c.get('text', '') for c in comments]
        text_counter = Counter(texts)
        duplicates = {text: count for text, count in text_counter.items() if count > 1}
        
        if duplicates:
            print(f"\n❌ 发现 {len(duplicates)} 个重复文本:")
            for text, count in duplicates.items():
                print(f"   重复 {count} 次: {text[:60]}...")
        else:
            print(f"\n✅ 未发现重复文本")
        
        # 分析作者重复
        authors = [c.get('user', {}).get('screen_name', '') for c in comments]
        author_counter = Counter(authors)
        
        print(f"\n👥 作者分析:")
        print(f"   独立作者数: {len([a for a in set(authors) if a])}")
        print(f"   总评论数: {len(comments)}")
        
        # 显示作者评论分布
        author_distribution = {author: count for author, count in author_counter.items() if count > 1 and author}
        if author_distribution:
            print(f"   多条评论的作者:")
            for author, count in sorted(author_distribution.items(), key=lambda x: x[1], reverse=True):
                print(f"     {author}: {count} 条评论")
        
        # 分析ID重复
        ids = [c.get('id', '') for c in comments]
        id_counter = Counter(ids)
        id_duplicates = {id_val: count for id_val, count in id_counter.items() if count > 1}
        
        if id_duplicates:
            print(f"\n❌ 发现 {len(id_duplicates)} 个重复ID:")
            for id_val, count in id_duplicates.items():
                print(f"   ID {id_val} 重复 {count} 次")
        else:
            print(f"\n✅ 未发现重复ID")
        
        # 显示前5条评论
        print(f"\n💬 评论示例 (前5条):")
        for i, comment in enumerate(comments[:5], 1):
            author = comment.get('user', {}).get('screen_name', '未知')
            text = comment.get('text', '')[:50] + "..."
            comment_id = comment.get('id', '')
            print(f"   {i}. [ID:{comment_id}] 【{author}】{text}")
        
        return {
            'total_comments': len(comments),
            'unique_texts': len(set(texts)),
            'unique_authors': len(set([a for a in authors if a])),
            'text_duplicates': len(duplicates),
            'id_duplicates': len(id_duplicates)
        }
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return None

async def test_deduplication():
    """测试去重功能"""
    print("🧪 测试评论去重功能")
    print("=" * 50)
    
    from main_advanced import CrawlerApp
    
    app = CrawlerApp(enable_anti_detection=True)
    
    # 测试参数
    test_symbol = "SH688775"
    target_count = 20
    
    print(f"📊 测试参数:")
    print(f"   股票代码: {test_symbol}")
    print(f"   目标评论数: {target_count}")
    print()
    
    try:
        start_time = datetime.now()
        result = await app.crawl_stock_comments(
            symbol=test_symbol,
            count=target_count,
            scroll_strategy="moderate"
        )
        end_time = datetime.now()
        
        duration = (end_time - start_time).total_seconds()
        
        if result.get('success'):
            api_data = result.get('api_format', {})
            comments = api_data.get('list', [])
            
            print(f"✅ 爬取成功!")
            print(f"📈 获取评论数: {len(comments)} 条")
            print(f"⏱️  耗时: {duration:.1f} 秒")
            
            # 分析重复情况
            texts = [c.get('text', '') for c in comments]
            authors = [c.get('user', {}).get('screen_name', '') for c in comments]
            ids = [c.get('id', '') for c in comments]
            
            unique_texts = len(set(texts))
            unique_authors = len(set([a for a in authors if a]))
            unique_ids = len(set(ids))
            
            print(f"\n📊 去重分析:")
            print(f"   总评论数: {len(comments)}")
            print(f"   唯一文本: {unique_texts}")
            print(f"   唯一作者: {unique_authors}")
            print(f"   唯一ID: {unique_ids}")
            
            # 检查重复
            text_duplicates = len(comments) - unique_texts
            id_duplicates = len(comments) - unique_ids
            
            if text_duplicates == 0 and id_duplicates == 0:
                print(f"🎉 去重功能正常: 无重复数据")
            else:
                print(f"⚠️  发现重复:")
                if text_duplicates > 0:
                    print(f"     重复文本: {text_duplicates} 条")
                if id_duplicates > 0:
                    print(f"     重复ID: {id_duplicates} 条")
            
            # 保存测试结果
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"dedup_test_result_{timestamp}.json"
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump([result], f, ensure_ascii=False, indent=2)
            
            print(f"\n📁 结果已保存: {filename}")
            
            # 分析保存的文件
            print(f"\n🔍 分析保存的结果文件:")
            analyze_duplicates_in_file(filename)
            
            return True
        else:
            print(f"❌ 爬取失败: {result.get('error')}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def compare_before_after():
    """比较优化前后的结果"""
    print(f"\n📊 比较优化前后的结果")
    print("=" * 50)
    
    # 分析现有的result.json文件
    if os.path.exists('result.json'):
        print("📁 分析现有的result.json (优化前):")
        old_stats = analyze_duplicates_in_file('result.json')
        return old_stats
    else:
        print("❌ 未找到result.json文件")
        return None

async def main():
    """主测试函数"""
    print("🚀 评论去重功能测试")
    print("=" * 60)
    
    # 1. 分析现有文件
    import os
    old_stats = compare_before_after()
    
    # 2. 测试新的去重功能
    success = await test_deduplication()
    
    if success and old_stats:
        print(f"\n📈 优化效果对比:")
        print("=" * 30)
        print(f"{'指标':<15} {'优化前':<10} {'优化后':<10} {'改进':<10}")
        print("-" * 50)
        
        # 这里需要新的统计数据来对比
        print("💡 请查看上面的详细分析结果")
    
    print(f"\n🎉 测试完成!")
    print("💡 去重功能说明:")
    print("   1. 基于元素唯一标识符避免重复处理")
    print("   2. 检查文本内容相似度")
    print("   3. 过滤相同作者的重复内容")
    print("   4. 最终去重确保数据唯一性")

if __name__ == "__main__":
    import os
    asyncio.run(main())

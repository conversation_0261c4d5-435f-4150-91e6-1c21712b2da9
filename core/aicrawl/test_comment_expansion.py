#!/usr/bin/env python3
"""
测试评论展开功能
验证是否能够获取完整的评论内容而不是折叠的片段
"""
import asyncio
import json
import logging
from datetime import datetime
import re

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def analyze_comment_content(comments):
    """分析评论内容，检测是否有折叠内容"""
    print("🔍 分析评论内容完整性")
    print("=" * 40)
    
    total_comments = len(comments)
    truncated_indicators = [
        '...', '…', '展开', '更多', '查看全文', '显示更多',
        'show more', 'read more', '点击展开'
    ]
    
    truncated_count = 0
    short_comments = 0
    long_comments = 0
    
    print(f"📊 评论内容分析:")
    print(f"   总评论数: {total_comments}")
    
    for i, comment in enumerate(comments, 1):
        text = comment.get('text', '')
        author = comment.get('user', {}).get('screen_name', '未知')
        
        # 检查是否包含截断指示符
        has_truncation = any(indicator in text for indicator in truncated_indicators)
        
        if has_truncation:
            truncated_count += 1
            print(f"   ⚠️  可能被截断 #{i}: 【{author}】{text[:60]}...")
        
        # 统计评论长度
        if len(text) < 30:
            short_comments += 1
        elif len(text) > 100:
            long_comments += 1
    
    print(f"\n📈 内容统计:")
    print(f"   可能截断: {truncated_count} 条 ({truncated_count/total_comments*100:.1f}%)")
    print(f"   短评论(<30字): {short_comments} 条")
    print(f"   长评论(>100字): {long_comments} 条")
    print(f"   平均长度: {sum(len(c.get('text', '')) for c in comments)/total_comments:.1f} 字符")
    
    # 显示最长和最短的评论
    if comments:
        longest = max(comments, key=lambda x: len(x.get('text', '')))
        shortest = min(comments, key=lambda x: len(x.get('text', '')))
        
        print(f"\n📝 内容示例:")
        print(f"   最长评论 ({len(longest.get('text', ''))} 字符):")
        print(f"     【{longest.get('user', {}).get('screen_name', '未知')}】{longest.get('text', '')[:100]}...")
        print(f"   最短评论 ({len(shortest.get('text', ''))} 字符):")
        print(f"     【{shortest.get('user', {}).get('screen_name', '未知')}】{shortest.get('text', '')}")
    
    return {
        'total': total_comments,
        'truncated': truncated_count,
        'short': short_comments,
        'long': long_comments,
        'avg_length': sum(len(c.get('text', '')) for c in comments)/total_comments if comments else 0
    }

async def test_comment_expansion():
    """测试评论展开功能"""
    print("🧪 测试评论展开功能")
    print("=" * 50)
    
    from main_advanced import CrawlerApp
    
    app = CrawlerApp(enable_anti_detection=True)
    
    # 测试参数
    test_symbol = "SH688775"  # 影石创新，通常有较多评论
    target_count = 25
    
    print(f"📊 测试参数:")
    print(f"   股票代码: {test_symbol}")
    print(f"   目标评论数: {target_count}")
    print(f"   滚动策略: moderate (含展开功能)")
    print()
    
    try:
        start_time = datetime.now()
        result = await app.crawl_stock_comments(
            symbol=test_symbol,
            count=target_count,
            scroll_strategy="moderate"
        )
        end_time = datetime.now()
        
        duration = (end_time - start_time).total_seconds()
        
        if result.get('success'):
            api_data = result.get('api_format', {})
            comments = api_data.get('list', [])
            
            print(f"✅ 爬取成功!")
            print(f"📈 获取评论数: {len(comments)} 条")
            print(f"⏱️  耗时: {duration:.1f} 秒")
            
            # 分析评论内容
            content_stats = analyze_comment_content(comments)
            
            # 评估展开效果
            print(f"\n📊 展开效果评估:")
            if content_stats['truncated'] == 0:
                print(f"🎉 优秀: 未发现截断内容")
            elif content_stats['truncated'] <= 2:
                print(f"✅ 良好: 仅 {content_stats['truncated']} 条可能截断")
            else:
                print(f"⚠️  需要改进: {content_stats['truncated']} 条可能截断")
            
            # 内容质量分析
            if content_stats['avg_length'] > 80:
                print(f"📝 内容质量: 优秀 (平均 {content_stats['avg_length']:.1f} 字符)")
            elif content_stats['avg_length'] > 50:
                print(f"📝 内容质量: 良好 (平均 {content_stats['avg_length']:.1f} 字符)")
            else:
                print(f"📝 内容质量: 一般 (平均 {content_stats['avg_length']:.1f} 字符)")
            
            # 保存测试结果
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"expansion_test_result_{timestamp}.json"
            
            test_result = {
                'test_info': {
                    'symbol': test_symbol,
                    'target_count': target_count,
                    'actual_count': len(comments),
                    'duration': duration,
                    'test_time': datetime.now().isoformat()
                },
                'content_analysis': content_stats,
                'comments': comments[:10]  # 保存前10条作为示例
            }
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(test_result, f, ensure_ascii=False, indent=2)
            
            print(f"\n📁 测试结果已保存: {filename}")
            
            return True
        else:
            print(f"❌ 爬取失败: {result.get('error')}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

async def compare_with_without_expansion():
    """比较有无展开功能的效果"""
    print(f"\n🔄 比较展开功能效果")
    print("=" * 50)
    
    from main_advanced import CrawlerApp
    
    app = CrawlerApp(enable_anti_detection=True)
    test_symbol = "SH688521"  # 芯原股份
    
    results = {}
    
    # 测试1: 使用保守策略 (展开功能较少)
    print(f"📊 测试1: 保守策略")
    try:
        result1 = await app.crawl_stock_comments(
            symbol=test_symbol,
            count=15,
            scroll_strategy="conservative"
        )
        
        if result1.get('success'):
            comments1 = result1.get('api_format', {}).get('list', [])
            stats1 = analyze_comment_content(comments1)
            results['conservative'] = stats1
            print(f"✅ 保守策略: {len(comments1)} 条评论")
        else:
            print(f"❌ 保守策略失败")
            
    except Exception as e:
        print(f"❌ 保守策略异常: {e}")
    
    # 等待间隔
    print("⏳ 等待5秒...")
    await asyncio.sleep(5)
    
    # 测试2: 使用适中策略 (完整展开功能)
    print(f"\n📊 测试2: 适中策略 (含展开)")
    try:
        result2 = await app.crawl_stock_comments(
            symbol=test_symbol,
            count=15,
            scroll_strategy="moderate"
        )
        
        if result2.get('success'):
            comments2 = result2.get('api_format', {}).get('list', [])
            stats2 = analyze_comment_content(comments2)
            results['moderate'] = stats2
            print(f"✅ 适中策略: {len(comments2)} 条评论")
        else:
            print(f"❌ 适中策略失败")
            
    except Exception as e:
        print(f"❌ 适中策略异常: {e}")
    
    # 对比结果
    if len(results) >= 2:
        print(f"\n📊 策略对比:")
        print(f"{'指标':<15} {'保守策略':<12} {'适中策略':<12} {'改进':<10}")
        print("-" * 55)
        
        for key in ['total', 'truncated', 'avg_length']:
            if key in results['conservative'] and key in results['moderate']:
                val1 = results['conservative'][key]
                val2 = results['moderate'][key]
                
                if key == 'truncated':
                    improvement = f"{val1-val2:+d}"
                else:
                    improvement = f"{val2-val1:+.1f}"
                
                print(f"{key:<15} {val1:<12.1f} {val2:<12.1f} {improvement:<10}")

async def main():
    """主测试函数"""
    print("🚀 评论展开功能测试")
    print("=" * 60)
    print("测试是否能够获取完整的评论内容而不是折叠的片段")
    print()
    
    # 测试1: 基础展开功能测试
    success = await test_comment_expansion()
    
    if success:
        # 测试2: 策略对比 (可选)
        try_compare = input(f"\n❓ 是否进行策略对比测试? (y/N): ").lower().strip()
        if try_compare == 'y':
            await compare_with_without_expansion()
    
    print(f"\n🎉 测试完成!")
    print("💡 展开功能说明:")
    print("   1. 预先展开: 页面加载后立即展开可见内容")
    print("   2. 滚动展开: 滚动过程中持续展开新出现的内容")
    print("   3. 智能识别: 多种选择器和关键词识别展开按钮")
    print("   4. 容错处理: 展开失败不影响整体爬取")

if __name__ == "__main__":
    asyncio.run(main())

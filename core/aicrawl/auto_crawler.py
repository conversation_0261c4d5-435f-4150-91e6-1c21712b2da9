"""
雪球全站自动爬取系统
集成热门自媒体内容和个股评论的自动化爬取
"""
import asyncio
import logging
import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Set
from dataclasses import dataclass, field
from pathlib import Path
import schedule
import threading
from concurrent.futures import ThreadPoolExecutor

from crawler_engine import SmartCrawler, CrawlResult
from extractors import XueqiuHomePageData, StockDetailData, CommentData
from config import AntiDetectionConfig
from data_storage import DataStorage, CrawlTask, CrawlResult as StoredResult
from monitoring import CrawlerMonitor, PerformanceMetrics

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('auto_crawler.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class AutoCrawlerConfig:
    """自动爬虫配置"""
    # 爬取间隔配置（分钟）
    homepage_interval: int = 30  # 首页热门内容每30分钟爬取一次
    stock_comments_interval: int = 60  # 个股评论每小时爬取一次
    trending_stocks_interval: int = 15  # 热门股票每15分钟爬取一次
    
    # 爬取数量配置
    max_comments_per_stock: int = 50  # 每个股票最多爬取50条评论
    max_trending_stocks: int = 100  # 最多跟踪100只热门股票
    max_homepage_posts: int = 200  # 首页最多爬取200条动态
    
    # 并发配置
    max_concurrent_tasks: int = 5  # 最大并发任务数
    max_stocks_per_batch: int = 10  # 每批处理的股票数量
    
    # 数据保留配置
    data_retention_days: int = 30  # 数据保留天数
    
    # 错误处理配置
    max_retries: int = 3  # 最大重试次数
    retry_delay: int = 300  # 重试延迟（秒）
    
    # 反爬虫配置
    enable_anti_detection: bool = True
    random_delay_range: tuple = (5, 15)  # 随机延迟范围（秒）

@dataclass
class CrawlTaskInfo:
    """爬取任务信息"""
    task_id: str
    task_type: str  # 'homepage', 'stock_comments', 'trending_stocks'
    target: str  # 目标（股票代码或URL）
    priority: int = 1  # 优先级（1-10，数字越大优先级越高）
    scheduled_time: datetime = field(default_factory=datetime.now)
    retry_count: int = 0
    last_error: Optional[str] = None
    status: str = 'pending'  # 'pending', 'running', 'completed', 'failed'

class AutoCrawlerScheduler:
    """自动爬虫调度器"""
    
    def __init__(self, config: AutoCrawlerConfig):
        self.config = config
        self.crawler = SmartCrawler(enable_anti_detection=config.enable_anti_detection)
        self.data_storage = DataStorage()
        self.monitor = CrawlerMonitor()
        
        # 任务队列和状态管理
        self.task_queue: List[CrawlTaskInfo] = []
        self.running_tasks: Set[str] = set()
        self.completed_tasks: Set[str] = set()
        
        # 线程池和调度器
        self.executor = ThreadPoolExecutor(max_workers=config.max_concurrent_tasks)
        self.scheduler_thread = None
        self.is_running = False
        
        # 热门股票缓存
        self.trending_stocks_cache: List[str] = []
        self.last_trending_update = datetime.min
        
        logger.info(f"自动爬虫调度器初始化完成，配置: {config}")

    async def start(self):
        """启动自动爬虫系统"""
        logger.info("启动雪球全站自动爬取系统...")
        
        self.is_running = True
        
        # 初始化数据存储
        await self.data_storage.initialize()
        
        # 设置定时任务
        self._setup_scheduled_tasks()
        
        # 启动调度器线程
        self.scheduler_thread = threading.Thread(target=self._run_scheduler, daemon=True)
        self.scheduler_thread.start()
        
        # 启动任务执行循环
        await self._run_task_executor()

    def _setup_scheduled_tasks(self):
        """设置定时任务"""
        # 首页热门内容爬取
        schedule.every(self.config.homepage_interval).minutes.do(
            self._schedule_homepage_crawl
        )
        
        # 热门股票发现
        schedule.every(self.config.trending_stocks_interval).minutes.do(
            self._schedule_trending_stocks_discovery
        )
        
        # 个股评论爬取
        schedule.every(self.config.stock_comments_interval).minutes.do(
            self._schedule_stock_comments_crawl
        )
        
        # 数据清理任务（每天凌晨2点）
        schedule.every().day.at("02:00").do(
            self._schedule_data_cleanup
        )
        
        # 立即执行一次初始化爬取
        self._schedule_homepage_crawl()
        self._schedule_trending_stocks_discovery()
        
        logger.info("定时任务设置完成")

    def _run_scheduler(self):
        """运行调度器"""
        while self.is_running:
            try:
                schedule.run_pending()
                time.sleep(60)  # 每分钟检查一次
            except Exception as e:
                logger.error(f"调度器运行错误: {e}")
                time.sleep(60)

    async def _run_task_executor(self):
        """运行任务执行器"""
        while self.is_running:
            try:
                # 处理待执行的任务
                await self._process_pending_tasks()
                
                # 等待一段时间再检查
                await asyncio.sleep(30)
                
            except Exception as e:
                logger.error(f"任务执行器错误: {e}")
                await asyncio.sleep(60)

    async def _process_pending_tasks(self):
        """处理待执行的任务"""
        # 获取待执行的任务（按优先级和时间排序）
        pending_tasks = [
            task for task in self.task_queue 
            if task.status == 'pending' and task.task_id not in self.running_tasks
            and task.scheduled_time <= datetime.now()
        ]
        
        # 按优先级排序
        pending_tasks.sort(key=lambda x: (-x.priority, x.scheduled_time))
        
        # 限制并发数量
        available_slots = self.config.max_concurrent_tasks - len(self.running_tasks)
        tasks_to_run = pending_tasks[:available_slots]
        
        # 执行任务
        for task in tasks_to_run:
            await self._execute_task(task)

    async def _execute_task(self, task: CrawlTaskInfo):
        """执行单个爬取任务"""
        task.status = 'running'
        self.running_tasks.add(task.task_id)
        
        try:
            logger.info(f"开始执行任务: {task.task_id} ({task.task_type})")
            
            # 根据任务类型执行不同的爬取逻辑
            if task.task_type == 'homepage':
                result = await self._crawl_homepage_content()
            elif task.task_type == 'stock_comments':
                result = await self._crawl_stock_comments(task.target)
            elif task.task_type == 'trending_stocks':
                result = await self._discover_trending_stocks()
            else:
                raise ValueError(f"未知任务类型: {task.task_type}")
            
            # 保存结果
            await self._save_crawl_result(task, result)
            
            task.status = 'completed'
            self.completed_tasks.add(task.task_id)
            
            logger.info(f"任务完成: {task.task_id}")
            
        except Exception as e:
            logger.error(f"任务执行失败: {task.task_id}, 错误: {e}")
            task.last_error = str(e)
            task.retry_count += 1
            
            # 判断是否需要重试
            if task.retry_count < self.config.max_retries:
                task.status = 'pending'
                task.scheduled_time = datetime.now() + timedelta(seconds=self.config.retry_delay)
                logger.info(f"任务将重试: {task.task_id} (第{task.retry_count}次)")
            else:
                task.status = 'failed'
                logger.error(f"任务最终失败: {task.task_id}")
        
        finally:
            self.running_tasks.discard(task.task_id)

    def _schedule_homepage_crawl(self):
        """调度首页内容爬取"""
        task = CrawlTaskInfo(
            task_id=f"homepage_{int(time.time())}",
            task_type='homepage',
            target='https://xueqiu.com',
            priority=8,  # 高优先级
            scheduled_time=datetime.now()
        )
        self.task_queue.append(task)
        logger.info(f"已调度首页爬取任务: {task.task_id}")

    def _schedule_trending_stocks_discovery(self):
        """调度热门股票发现"""
        task = CrawlTaskInfo(
            task_id=f"trending_{int(time.time())}",
            task_type='trending_stocks',
            target='discovery',
            priority=7,  # 较高优先级
            scheduled_time=datetime.now()
        )
        self.task_queue.append(task)
        logger.info(f"已调度热门股票发现任务: {task.task_id}")

    def _schedule_stock_comments_crawl(self):
        """调度个股评论爬取"""
        # 为每个热门股票创建评论爬取任务
        for i, stock_code in enumerate(self.trending_stocks_cache[:self.config.max_stocks_per_batch]):
            task = CrawlTaskInfo(
                task_id=f"comments_{stock_code}_{int(time.time())}",
                task_type='stock_comments',
                target=stock_code,
                priority=5,  # 中等优先级
                scheduled_time=datetime.now() + timedelta(seconds=i * 10)  # 错开执行时间
            )
            self.task_queue.append(task)
        
        logger.info(f"已调度 {len(self.trending_stocks_cache[:self.config.max_stocks_per_batch])} 个股票评论爬取任务")

    def _schedule_data_cleanup(self):
        """调度数据清理任务"""
        task = CrawlTaskInfo(
            task_id=f"cleanup_{int(time.time())}",
            task_type='data_cleanup',
            target='all',
            priority=1,  # 低优先级
            scheduled_time=datetime.now()
        )
        self.task_queue.append(task)
        logger.info(f"已调度数据清理任务: {task.task_id}")

    async def _crawl_homepage_content(self) -> Dict[str, Any]:
        """爬取首页热门内容"""
        logger.info("开始爬取雪球首页热门内容...")

        try:
            # 使用增强模式爬取首页
            result = await self.crawler.crawl_homepage(enhanced_mode=True)

            if result.success and result.data:
                homepage_data = result.data

                # 提取关键信息
                crawl_result = {
                    'success': True,
                    'data_type': 'homepage',
                    'crawl_time': datetime.now().isoformat(),
                    'hot_stocks': self._extract_hot_stocks(homepage_data.hot_stocks),
                    'hot_topics': self._extract_hot_topics(homepage_data.hot_topics),
                    'user_posts': self._extract_user_posts(homepage_data.user_posts),
                    'market_summary': homepage_data.market_summary,
                    'total_items': {
                        'hot_stocks': len(homepage_data.hot_stocks) if homepage_data.hot_stocks else 0,
                        'hot_topics': len(homepage_data.hot_topics) if homepage_data.hot_topics else 0,
                        'user_posts': len(homepage_data.user_posts) if homepage_data.user_posts else 0
                    }
                }

                # 更新热门股票缓存
                if homepage_data.hot_stocks:
                    new_trending_stocks = [stock.symbol for stock in homepage_data.hot_stocks[:self.config.max_trending_stocks]]
                    self.trending_stocks_cache = list(set(self.trending_stocks_cache + new_trending_stocks))
                    self.last_trending_update = datetime.now()

                logger.info(f"首页爬取成功: 热门股票{len(homepage_data.hot_stocks or [])}个, "
                           f"热门话题{len(homepage_data.hot_topics or [])}个, "
                           f"用户动态{len(homepage_data.user_posts or [])}条")

                return crawl_result
            else:
                raise Exception(f"首页爬取失败: {result.error}")

        except Exception as e:
            logger.error(f"首页内容爬取异常: {e}")
            raise

    async def _crawl_stock_comments(self, stock_code: str) -> Dict[str, Any]:
        """爬取个股评论"""
        logger.info(f"开始爬取股票 {stock_code} 的评论...")

        try:
            # 使用增强的评论爬取策略
            from main_advanced import CrawlerApp
            app = CrawlerApp(enable_anti_detection=self.config.enable_anti_detection)

            result = await app.crawl_stock_comments(
                stock_code,
                count=self.config.max_comments_per_stock,
                scroll_strategy="moderate"
            )

            if result.get('success'):
                comments_data = result.get('api_format', {}).get('list', [])

                crawl_result = {
                    'success': True,
                    'data_type': 'stock_comments',
                    'stock_code': stock_code,
                    'crawl_time': datetime.now().isoformat(),
                    'comments': self._process_comments_data(comments_data),
                    'total_comments': len(comments_data),
                    'response_time': result.get('response_time', 0)
                }

                logger.info(f"股票 {stock_code} 评论爬取成功: {len(comments_data)} 条评论")
                return crawl_result
            else:
                raise Exception(f"股票评论爬取失败: {result.get('error')}")

        except Exception as e:
            logger.error(f"股票 {stock_code} 评论爬取异常: {e}")
            raise

    async def _discover_trending_stocks(self) -> Dict[str, Any]:
        """发现热门股票"""
        logger.info("开始发现热门股票...")

        try:
            # 爬取多个来源的热门股票
            sources = [
                'https://xueqiu.com/hq',  # 行情页面
                'https://xueqiu.com/today',  # 今日热门
            ]

            all_trending_stocks = []

            for source_url in sources:
                try:
                    result = await self.crawler.crawl_url(source_url)
                    if result.success:
                        # 从页面提取股票代码
                        stocks = self._extract_stocks_from_page(result.raw_html)
                        all_trending_stocks.extend(stocks)
                        logger.info(f"从 {source_url} 发现 {len(stocks)} 只股票")
                except Exception as e:
                    logger.warning(f"从 {source_url} 发现股票失败: {e}")

            # 去重并限制数量
            unique_stocks = list(set(all_trending_stocks))[:self.config.max_trending_stocks]

            # 更新缓存
            self.trending_stocks_cache = unique_stocks
            self.last_trending_update = datetime.now()

            crawl_result = {
                'success': True,
                'data_type': 'trending_stocks',
                'crawl_time': datetime.now().isoformat(),
                'trending_stocks': unique_stocks,
                'total_stocks': len(unique_stocks),
                'sources_checked': len(sources)
            }

            logger.info(f"热门股票发现完成: 共发现 {len(unique_stocks)} 只热门股票")
            return crawl_result

        except Exception as e:
            logger.error(f"热门股票发现异常: {e}")
            raise

    def _extract_hot_stocks(self, hot_stocks) -> List[Dict[str, Any]]:
        """提取热门股票信息"""
        if not hot_stocks:
            return []

        return [
            {
                'symbol': stock.symbol,
                'name': stock.name,
                'current_price': stock.current_price,
                'change_percent': stock.change_percent,
                'rank': stock.rank,
                'volume': getattr(stock, 'volume', None),
                'market_cap': getattr(stock, 'market_cap', None)
            }
            for stock in hot_stocks[:50]  # 限制数量
        ]

    def _extract_hot_topics(self, hot_topics) -> List[Dict[str, Any]]:
        """提取热门话题信息"""
        if not hot_topics:
            return []

        return [
            {
                'title': topic.title,
                'author': topic.author,
                'view_count': topic.view_count,
                'comment_count': topic.comment_count,
                'url': topic.url,
                'publish_time': getattr(topic, 'publish_time', None)
            }
            for topic in hot_topics[:100]  # 限制数量
        ]

    def _extract_user_posts(self, user_posts) -> List[Dict[str, Any]]:
        """提取用户动态信息"""
        if not user_posts:
            return []

        return [
            {
                'author': post.author,
                'content': post.content[:500] + "..." if len(post.content) > 500 else post.content,
                'publish_time': post.publish_time,
                'like_count': post.like_count,
                'comment_count': post.comment_count,
                'mentioned_stocks': post.mentioned_stocks,
                'is_repost': post.is_repost,
                'images_count': len(post.images) if post.images else 0,
                'url': getattr(post, 'url', None)
            }
            for post in user_posts[:self.config.max_homepage_posts]
        ]

    def _process_comments_data(self, comments_data) -> List[Dict[str, Any]]:
        """处理评论数据"""
        processed_comments = []

        for comment in comments_data:
            try:
                processed_comment = {
                    'id': comment.get('id', ''),
                    'text': comment.get('text', ''),
                    'author': comment.get('user', {}).get('screen_name', ''),
                    'author_id': comment.get('user', {}).get('id', ''),
                    'created_at': comment.get('created_at', ''),
                    'like_count': comment.get('fav_count', 0),
                    'reply_count': comment.get('reply_count', 0),
                    'sentiment': self._analyze_sentiment(comment.get('text', '')),
                    'mentioned_stocks': self._extract_mentioned_stocks(comment.get('text', ''))
                }
                processed_comments.append(processed_comment)
            except Exception as e:
                logger.warning(f"处理评论数据失败: {e}")
                continue

        return processed_comments

    def _extract_stocks_from_page(self, html_content: str) -> List[str]:
        """从页面HTML中提取股票代码"""
        import re
        from bs4 import BeautifulSoup

        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            stock_codes = []

            # 查找股票代码的多种模式
            patterns = [
                r'[SZ|SH]\d{6}',  # 标准股票代码格式
                r'\$[A-Z]{2,5}\$',  # 雪球股票标记格式
                r'symbol["\']:\s*["\']([SZ|SH]\d{6})["\']',  # JSON中的股票代码
            ]

            for pattern in patterns:
                matches = re.findall(pattern, html_content, re.IGNORECASE)
                stock_codes.extend(matches)

            # 从链接中提取股票代码
            links = soup.find_all('a', href=True)
            for link in links:
                href = link['href']
                if '/S/' in href:
                    match = re.search(r'/S/([SZ|SH]\d{6})', href)
                    if match:
                        stock_codes.append(match.group(1))

            # 去重并返回
            return list(set(stock_codes))

        except Exception as e:
            logger.warning(f"从页面提取股票代码失败: {e}")
            return []

    def _analyze_sentiment(self, text: str) -> str:
        """简单的情感分析"""
        if not text:
            return 'neutral'

        positive_words = ['涨', '牛', '好', '买入', '看好', '推荐', '利好', '上涨']
        negative_words = ['跌', '熊', '差', '卖出', '看空', '不好', '利空', '下跌']

        positive_count = sum(1 for word in positive_words if word in text)
        negative_count = sum(1 for word in negative_words if word in text)

        if positive_count > negative_count:
            return 'positive'
        elif negative_count > positive_count:
            return 'negative'
        else:
            return 'neutral'

    def _extract_mentioned_stocks(self, text: str) -> List[str]:
        """从文本中提取提到的股票代码"""
        import re

        patterns = [
            r'[SZ|SH]\d{6}',
            r'\$([A-Z]{2,5})\$'
        ]

        mentioned_stocks = []
        for pattern in patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            mentioned_stocks.extend(matches)

        return list(set(mentioned_stocks))

    async def _save_crawl_result(self, task: CrawlTaskInfo, result: Dict[str, Any]):
        """保存爬取结果"""
        try:
            # 保存到数据存储系统
            await self.data_storage.save_crawl_result(
                task_id=task.task_id,
                task_type=task.task_type,
                target=task.target,
                result=result,
                crawl_time=datetime.now()
            )

            # 记录性能指标
            self.monitor.record_task_completion(task, result)

            # 根据数据类型进行特殊处理
            if task.task_type == 'homepage' and result.get('success'):
                await self._process_homepage_result(result)
            elif task.task_type == 'stock_comments' and result.get('success'):
                await self._process_comments_result(task.target, result)
            elif task.task_type == 'trending_stocks' and result.get('success'):
                await self._process_trending_stocks_result(result)

            logger.info(f"爬取结果已保存: {task.task_id}")

        except Exception as e:
            logger.error(f"保存爬取结果失败: {task.task_id}, 错误: {e}")
            raise

    async def _process_homepage_result(self, result: Dict[str, Any]):
        """处理首页爬取结果"""
        try:
            # 提取并保存热门股票信息
            hot_stocks = result.get('hot_stocks', [])
            for stock in hot_stocks:
                await self.data_storage.save_hot_stock(stock)

            # 提取并保存热门话题
            hot_topics = result.get('hot_topics', [])
            for topic in hot_topics:
                await self.data_storage.save_hot_topic(topic)

            # 提取并保存用户动态
            user_posts = result.get('user_posts', [])
            for post in user_posts:
                await self.data_storage.save_user_post(post)

            logger.info(f"首页结果处理完成: 股票{len(hot_stocks)}个, 话题{len(hot_topics)}个, 动态{len(user_posts)}条")

        except Exception as e:
            logger.error(f"处理首页结果失败: {e}")

    async def _process_comments_result(self, stock_code: str, result: Dict[str, Any]):
        """处理评论爬取结果"""
        try:
            comments = result.get('comments', [])

            # 保存评论数据
            for comment in comments:
                comment['stock_code'] = stock_code
                await self.data_storage.save_stock_comment(comment)

            # 更新股票的评论统计
            await self.data_storage.update_stock_comment_stats(stock_code, len(comments))

            logger.info(f"股票 {stock_code} 评论结果处理完成: {len(comments)} 条评论")

        except Exception as e:
            logger.error(f"处理股票 {stock_code} 评论结果失败: {e}")

    async def _process_trending_stocks_result(self, result: Dict[str, Any]):
        """处理热门股票发现结果"""
        try:
            trending_stocks = result.get('trending_stocks', [])

            # 更新热门股票列表
            await self.data_storage.update_trending_stocks(trending_stocks)

            logger.info(f"热门股票结果处理完成: {len(trending_stocks)} 只股票")

        except Exception as e:
            logger.error(f"处理热门股票结果失败: {e}")

    async def stop(self):
        """停止自动爬虫系统"""
        logger.info("正在停止雪球全站自动爬取系统...")

        self.is_running = False

        # 等待调度器线程结束
        if self.scheduler_thread and self.scheduler_thread.is_alive():
            self.scheduler_thread.join(timeout=10)

        # 关闭线程池
        self.executor.shutdown(wait=True)

        # 关闭数据存储
        await self.data_storage.close()

        logger.info("自动爬虫系统已停止")

    def get_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        return {
            'is_running': self.is_running,
            'task_queue_size': len(self.task_queue),
            'running_tasks_count': len(self.running_tasks),
            'completed_tasks_count': len(self.completed_tasks),
            'trending_stocks_count': len(self.trending_stocks_cache),
            'last_trending_update': self.last_trending_update.isoformat() if self.last_trending_update != datetime.min else None,
            'performance_metrics': self.monitor.get_metrics(),
            'config': {
                'homepage_interval': self.config.homepage_interval,
                'stock_comments_interval': self.config.stock_comments_interval,
                'trending_stocks_interval': self.config.trending_stocks_interval,
                'max_concurrent_tasks': self.config.max_concurrent_tasks
            }
        }

    def get_recent_tasks(self, limit: int = 20) -> List[Dict[str, Any]]:
        """获取最近的任务信息"""
        recent_tasks = sorted(
            self.task_queue,
            key=lambda x: x.scheduled_time,
            reverse=True
        )[:limit]

        return [
            {
                'task_id': task.task_id,
                'task_type': task.task_type,
                'target': task.target,
                'status': task.status,
                'priority': task.priority,
                'scheduled_time': task.scheduled_time.isoformat(),
                'retry_count': task.retry_count,
                'last_error': task.last_error
            }
            for task in recent_tasks
        ]

# 主程序入口
async def main():
    """主程序"""
    # 创建配置
    config = AutoCrawlerConfig(
        homepage_interval=30,  # 30分钟
        stock_comments_interval=60,  # 1小时
        trending_stocks_interval=15,  # 15分钟
        max_concurrent_tasks=3,  # 降低并发数避免被封
        max_comments_per_stock=30,  # 每股30条评论
        max_trending_stocks=50,  # 跟踪50只热门股票
        enable_anti_detection=True
    )

    # 创建调度器
    scheduler = AutoCrawlerScheduler(config)

    try:
        # 启动系统
        await scheduler.start()
    except KeyboardInterrupt:
        logger.info("收到中断信号，正在停止系统...")
        await scheduler.stop()
    except Exception as e:
        logger.error(f"系统运行异常: {e}")
        await scheduler.stop()
        raise

if __name__ == "__main__":
    asyncio.run(main())

"""
监控模块
负责系统性能监控、错误跟踪和报警
"""
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, field
from collections import defaultdict, deque
import json

logger = logging.getLogger(__name__)

@dataclass
class PerformanceMetrics:
    """性能指标数据结构"""
    total_tasks: int = 0
    successful_tasks: int = 0
    failed_tasks: int = 0
    average_response_time: float = 0.0
    success_rate: float = 0.0
    tasks_per_hour: float = 0.0
    error_rate: float = 0.0
    last_update: datetime = field(default_factory=datetime.now)

@dataclass
class TaskMetrics:
    """任务指标"""
    task_type: str
    start_time: datetime
    end_time: Optional[datetime] = None
    success: bool = False
    response_time: float = 0.0
    error_message: Optional[str] = None
    data_size: int = 0

class CrawlerMonitor:
    """爬虫监控器"""
    
    def __init__(self, max_history_size: int = 1000):
        self.max_history_size = max_history_size
        
        # 性能指标
        self.metrics = PerformanceMetrics()
        
        # 任务历史记录
        self.task_history: deque = deque(maxlen=max_history_size)
        
        # 错误统计
        self.error_stats = defaultdict(int)
        self.recent_errors: deque = deque(maxlen=100)
        
        # 实时统计
        self.hourly_stats = defaultdict(lambda: {'success': 0, 'failed': 0, 'total_time': 0.0})
        
        # 系统状态
        self.system_status = {
            'status': 'running',
            'start_time': datetime.now(),
            'last_activity': datetime.now()
        }
        
        logger.info("爬虫监控器初始化完成")

    def record_task_start(self, task_id: str, task_type: str) -> TaskMetrics:
        """记录任务开始"""
        task_metrics = TaskMetrics(
            task_type=task_type,
            start_time=datetime.now()
        )
        
        logger.debug(f"任务开始监控: {task_id} ({task_type})")
        return task_metrics

    def record_task_completion(self, task, result: Dict[str, Any]):
        """记录任务完成"""
        try:
            end_time = datetime.now()
            success = result.get('success', False)
            
            # 计算响应时间
            if hasattr(task, 'scheduled_time'):
                response_time = (end_time - task.scheduled_time).total_seconds()
            else:
                response_time = result.get('response_time', 0.0)
            
            # 创建任务指标
            task_metrics = TaskMetrics(
                task_type=task.task_type,
                start_time=task.scheduled_time if hasattr(task, 'scheduled_time') else end_time,
                end_time=end_time,
                success=success,
                response_time=response_time,
                error_message=task.last_error if hasattr(task, 'last_error') else None,
                data_size=self._calculate_data_size(result)
            )
            
            # 添加到历史记录
            self.task_history.append(task_metrics)
            
            # 更新统计信息
            self._update_metrics(task_metrics)
            
            # 记录错误
            if not success and task_metrics.error_message:
                self._record_error(task.task_type, task_metrics.error_message)
            
            # 更新系统状态
            self.system_status['last_activity'] = end_time
            
            logger.debug(f"任务完成监控: {task.task_id} (成功: {success}, 耗时: {response_time:.2f}s)")
            
        except Exception as e:
            logger.error(f"记录任务完成失败: {e}")

    def _calculate_data_size(self, result: Dict[str, Any]) -> int:
        """计算数据大小"""
        try:
            if result.get('success'):
                # 根据数据类型计算大小
                data_type = result.get('data_type', '')
                
                if data_type == 'homepage':
                    total_items = result.get('total_items', {})
                    return sum(total_items.values())
                elif data_type == 'stock_comments':
                    return result.get('total_comments', 0)
                elif data_type == 'trending_stocks':
                    return result.get('total_stocks', 0)
                else:
                    return len(str(result))
            return 0
        except:
            return 0

    def _update_metrics(self, task_metrics: TaskMetrics):
        """更新性能指标"""
        try:
            # 更新总体指标
            self.metrics.total_tasks += 1
            
            if task_metrics.success:
                self.metrics.successful_tasks += 1
            else:
                self.metrics.failed_tasks += 1
            
            # 计算成功率
            if self.metrics.total_tasks > 0:
                self.metrics.success_rate = self.metrics.successful_tasks / self.metrics.total_tasks
                self.metrics.error_rate = self.metrics.failed_tasks / self.metrics.total_tasks
            
            # 更新平均响应时间
            total_time = sum(tm.response_time for tm in self.task_history if tm.response_time > 0)
            valid_tasks = len([tm for tm in self.task_history if tm.response_time > 0])
            
            if valid_tasks > 0:
                self.metrics.average_response_time = total_time / valid_tasks
            
            # 计算每小时任务数
            recent_tasks = [
                tm for tm in self.task_history 
                if tm.start_time > datetime.now() - timedelta(hours=1)
            ]
            self.metrics.tasks_per_hour = len(recent_tasks)
            
            # 更新小时统计
            hour_key = task_metrics.start_time.strftime('%Y-%m-%d-%H')
            if task_metrics.success:
                self.hourly_stats[hour_key]['success'] += 1
            else:
                self.hourly_stats[hour_key]['failed'] += 1
            
            self.hourly_stats[hour_key]['total_time'] += task_metrics.response_time
            
            self.metrics.last_update = datetime.now()
            
        except Exception as e:
            logger.error(f"更新性能指标失败: {e}")

    def _record_error(self, task_type: str, error_message: str):
        """记录错误信息"""
        try:
            error_key = f"{task_type}:{error_message[:100]}"
            self.error_stats[error_key] += 1
            
            error_record = {
                'task_type': task_type,
                'error_message': error_message,
                'timestamp': datetime.now().isoformat(),
                'count': self.error_stats[error_key]
            }
            
            self.recent_errors.append(error_record)
            
            # 检查是否需要报警
            self._check_alert_conditions(task_type, error_message)
            
        except Exception as e:
            logger.error(f"记录错误信息失败: {e}")

    def _check_alert_conditions(self, task_type: str, error_message: str):
        """检查报警条件"""
        try:
            # 检查错误率是否过高
            if self.metrics.error_rate > 0.5 and self.metrics.total_tasks > 10:
                logger.warning(f"错误率过高: {self.metrics.error_rate:.2%}")
            
            # 检查特定错误是否频繁出现
            error_key = f"{task_type}:{error_message[:100]}"
            if self.error_stats[error_key] > 5:
                logger.warning(f"错误频繁出现: {error_key} (次数: {self.error_stats[error_key]})")
            
            # 检查响应时间是否过长
            if self.metrics.average_response_time > 60:  # 超过60秒
                logger.warning(f"平均响应时间过长: {self.metrics.average_response_time:.2f}秒")
            
        except Exception as e:
            logger.error(f"检查报警条件失败: {e}")

    def get_metrics(self) -> Dict[str, Any]:
        """获取性能指标"""
        return {
            'total_tasks': self.metrics.total_tasks,
            'successful_tasks': self.metrics.successful_tasks,
            'failed_tasks': self.metrics.failed_tasks,
            'success_rate': round(self.metrics.success_rate, 4),
            'error_rate': round(self.metrics.error_rate, 4),
            'average_response_time': round(self.metrics.average_response_time, 2),
            'tasks_per_hour': self.metrics.tasks_per_hour,
            'last_update': self.metrics.last_update.isoformat(),
            'system_status': self.system_status
        }

    def get_error_summary(self) -> Dict[str, Any]:
        """获取错误摘要"""
        # 获取最常见的错误
        top_errors = sorted(
            self.error_stats.items(),
            key=lambda x: x[1],
            reverse=True
        )[:10]
        
        # 获取最近的错误
        recent_errors = list(self.recent_errors)[-10:]
        
        return {
            'total_error_types': len(self.error_stats),
            'top_errors': [
                {'error': error, 'count': count}
                for error, count in top_errors
            ],
            'recent_errors': recent_errors,
            'error_rate_trend': self._get_error_rate_trend()
        }

    def _get_error_rate_trend(self) -> List[Dict[str, Any]]:
        """获取错误率趋势"""
        try:
            trend = []
            
            # 按小时统计错误率
            for hour_key, stats in sorted(self.hourly_stats.items())[-24:]:  # 最近24小时
                total = stats['success'] + stats['failed']
                error_rate = stats['failed'] / total if total > 0 else 0
                
                trend.append({
                    'hour': hour_key,
                    'total_tasks': total,
                    'error_rate': round(error_rate, 4),
                    'average_time': round(stats['total_time'] / total, 2) if total > 0 else 0
                })
            
            return trend
            
        except Exception as e:
            logger.error(f"获取错误率趋势失败: {e}")
            return []

    def get_task_type_stats(self) -> Dict[str, Any]:
        """获取按任务类型的统计"""
        try:
            type_stats = defaultdict(lambda: {'success': 0, 'failed': 0, 'total_time': 0.0})
            
            for task in self.task_history:
                if task.success:
                    type_stats[task.task_type]['success'] += 1
                else:
                    type_stats[task.task_type]['failed'] += 1
                
                type_stats[task.task_type]['total_time'] += task.response_time
            
            # 格式化结果
            result = {}
            for task_type, stats in type_stats.items():
                total = stats['success'] + stats['failed']
                result[task_type] = {
                    'total_tasks': total,
                    'success_rate': round(stats['success'] / total, 4) if total > 0 else 0,
                    'average_time': round(stats['total_time'] / total, 2) if total > 0 else 0,
                    'successful_tasks': stats['success'],
                    'failed_tasks': stats['failed']
                }
            
            return result
            
        except Exception as e:
            logger.error(f"获取任务类型统计失败: {e}")
            return {}

    def get_recent_activity(self, limit: int = 20) -> List[Dict[str, Any]]:
        """获取最近活动"""
        try:
            recent_tasks = list(self.task_history)[-limit:]
            
            return [
                {
                    'task_type': task.task_type,
                    'start_time': task.start_time.isoformat(),
                    'end_time': task.end_time.isoformat() if task.end_time else None,
                    'success': task.success,
                    'response_time': round(task.response_time, 2),
                    'data_size': task.data_size,
                    'error_message': task.error_message
                }
                for task in recent_tasks
            ]
            
        except Exception as e:
            logger.error(f"获取最近活动失败: {e}")
            return []

    def export_metrics(self, filepath: str):
        """导出监控指标到文件"""
        try:
            export_data = {
                'metrics': self.get_metrics(),
                'error_summary': self.get_error_summary(),
                'task_type_stats': self.get_task_type_stats(),
                'recent_activity': self.get_recent_activity(100),
                'export_time': datetime.now().isoformat()
            }
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"监控指标已导出到: {filepath}")
            
        except Exception as e:
            logger.error(f"导出监控指标失败: {e}")

    def reset_metrics(self):
        """重置监控指标"""
        self.metrics = PerformanceMetrics()
        self.task_history.clear()
        self.error_stats.clear()
        self.recent_errors.clear()
        self.hourly_stats.clear()
        
        self.system_status = {
            'status': 'running',
            'start_time': datetime.now(),
            'last_activity': datetime.now()
        }
        
        logger.info("监控指标已重置")

"""
高级爬虫主程序 - 集成完整的反爬虫功能
"""
import asyncio
import logging
import json
import argparse
from typing import List, Dict, Any
from pathlib import Path

from crawler_engine import SmartCrawler, CrawlResult
from extractors import XueqiuHomePageData, StockDetailData, CommentData
from config import AntiDetectionConfig

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('crawler.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class CrawlerApp:
    """爬虫应用主类"""
    
    def __init__(self, enable_anti_detection: bool = True):
        self.crawler = SmartCrawler(enable_anti_detection)
        self.results = []
        
    async def crawl_single_stock(self, symbol: str) -> Dict[str, Any]:
        """爬取单个股票"""
        logger.info(f"开始爬取股票: {symbol}")
        
        result = await self.crawler.crawl_stock(symbol)
        
        if result.success and result.data:
            logger.info(f"成功爬取 {symbol}: {result.data.name} - ¥{result.data.current_price}")
            return self._format_result(result)
        else:
            logger.error(f"爬取失败 {symbol}: {result.error}")
            return {"symbol": symbol, "error": result.error, "success": False}
    
    async def crawl_multiple_stocks(self, symbols: List[str], 
                                  concurrent_limit: int = 3) -> List[Dict[str, Any]]:
        """批量爬取股票"""
        logger.info(f"开始批量爬取 {len(symbols)} 个股票")
        
        results = await self.crawler.crawl_multiple_stocks(
            symbols, 
            concurrent_limit=concurrent_limit
        )
        
        formatted_results = []
        for result in results:
            formatted_results.append(self._format_result(result))
            
        # 打印性能报告
        report = self.crawler.get_performance_report()
        logger.info(f"爬取完成 - 性能报告: {report}")
        
        return formatted_results
    
    def _format_result(self, result: CrawlResult) -> Dict[str, Any]:
        """格式化结果"""
        if result.success and result.data:
            return {
                "success": True,
                "symbol": result.data.symbol,
                "name": result.data.name,
                "current_price": result.data.current_price,
                "change": result.data.change,
                "change_percent": result.data.change_percent,
                "volume": result.data.volume,
                "market_cap": result.data.market_cap,
                "pe_ttm": result.data.pe_ttm,
                "pb": result.data.pb,
                "response_time": result.response_time,
                "url": result.url
            }
        else:
            return {
                "success": False,
                "error": result.error,
                "url": result.url,
                "response_time": result.response_time
            }
    
    def save_results(self, results: List[Dict[str, Any]], filename: str = None):
        """保存结果到文件"""
        if not filename:
            filename = f"crawl_results_{int(asyncio.get_event_loop().time())}.json"
            
        output_path = Path(filename)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
            
        logger.info(f"结果已保存到: {output_path}")

        # 尝试保存为CSV格式（如果pandas可用）
        try:
            self._save_as_csv(results, output_path.with_suffix('.csv'))
        except Exception as e:
            logger.warning(f"CSV保存失败（pandas兼容性问题），已跳过: {str(e)[:100]}...")
            logger.info("JSON文件保存成功，可以正常使用")
    
    def _save_as_csv(self, results: List[Dict[str, Any]], csv_path: Path):
        """保存为CSV格式"""
        try:
            import pandas as pd
            
            # 过滤成功的结果
            successful_results = [r for r in results if r.get('success')]
            
            if successful_results:
                df = pd.DataFrame(successful_results)
                df.to_csv(csv_path, index=False, encoding='utf-8')
                logger.info(f"CSV结果已保存到: {csv_path}")
        except ImportError:
            logger.warning("pandas未安装，跳过CSV保存")

    async def crawl_homepage(self) -> Dict[str, Any]:
        """爬取雪球首页"""
        logger.info("开始爬取雪球首页")

        result = await self.crawler.crawl_homepage()

        if result.success and result.data:
            logger.info("成功爬取首页数据")
            return self._format_homepage_result(result)
        else:
            logger.error(f"首页爬取失败: {result.error}")
            return {"success": False, "error": result.error}

    async def crawl_homepage_enhanced(self) -> Dict[str, Any]:
        """增强版首页爬取（支持展开和滚动）"""
        logger.info("开始增强版首页爬取（支持展开和滚动）")

        result = await self.crawler.crawl_homepage(enhanced_mode=True)

        if result.success and result.data:
            logger.info("成功爬取增强版首页数据")
            formatted_result = self._format_homepage_result(result)
            formatted_result["enhanced_mode"] = True
            return formatted_result
        else:
            logger.error(f"增强版首页爬取失败: {result.error}")
            return {"success": False, "error": result.error, "enhanced_mode": True}

    async def crawl_stock_detail(self, symbol: str) -> Dict[str, Any]:
        """爬取个股详细信息"""
        logger.info(f"开始爬取股票详细信息: {symbol}")

        result = await self.crawler.crawl_stock_detail(symbol)

        if result.success and result.data:
            logger.info(f"成功爬取 {symbol} 详细信息")
            return self._format_detail_result(result)
        else:
            logger.error(f"详细信息爬取失败 {symbol}: {result.error}")
            return {"symbol": symbol, "success": False, "error": result.error}

    async def crawl_stock_comments(self, symbol: str, count: int = 10, scroll_strategy: str = "moderate") -> Dict[str, Any]:
        """爬取个股评论 - 使用增强提取策略"""
        logger.info(f"开始爬取股票评论: {symbol} (滚动策略: {scroll_strategy})")

        # 使用增强的JavaScript执行策略
        try:
            result = await self._crawl_comments_with_enhanced_js(symbol, count, scroll_strategy)

            if result.success and result.data:
                logger.info(f"成功爬取 {symbol} 评论 {len(result.data)} 条")
                return self._format_comments_result(result, symbol)
            else:
                logger.error(f"评论爬取失败 {symbol}: {result.error}")
                return {"symbol": symbol, "success": False, "error": result.error}

        except Exception as e:
            logger.error(f"评论爬取异常 {symbol}: {e}")
            return {"symbol": symbol, "success": False, "error": str(e)}

    async def _crawl_comments_with_enhanced_js(self, symbol: str, count: int, scroll_strategy: str = "moderate"):
        """使用增强JavaScript策略爬取评论"""
        from crawler_engine import CrawlResult, XueqiuCrawler
        from config import ScrollConfig

        # 创建专门的雪球爬虫实例
        xueqiu_crawler = XueqiuCrawler(enable_anti_detection=True)

        # 构建URL
        url = f"https://xueqiu.com/S/{symbol}"

        # 使用配置化的智能滚动策略
        enhanced_js_code = [
            # 等待页面完全加载
            "await new Promise(resolve => setTimeout(resolve, 3000));",

            # 尝试点击讨论标签
            """
            const tabs = document.querySelectorAll('[data-tab], .tab, .timeline-tab');
            for (let tab of tabs) {
                if (tab.textContent.includes('讨论') || tab.textContent.includes('评论') ||
                    tab.getAttribute('data-tab') === 'discuss') {
                    tab.click();
                    await new Promise(resolve => setTimeout(resolve, 2000));
                    break;
                }
            }
            """,

            # 预先展开所有可见的折叠内容
            """
            (async function() {
                console.log('🔍 预先展开折叠内容...');

                const expandSelectors = [
                    '.expand', '.show-more', '.unfold', '[class*="expand"]',
                    '[class*="unfold"]', '[class*="show-more"]', '[class*="展开"]',
                    '[class*="更多"]', 'a[class*="more"]', 'span[class*="more"]'
                ];

                const expandKeywords = [
                    '展开', '更多', '全文', '查看全文', '显示更多',
                    'expand', 'show more', 'read more', 'unfold'
                ];

                let totalExpanded = 0;

                for (let selector of expandSelectors) {
                    try {
                        const buttons = document.querySelectorAll(selector);
                        for (let btn of buttons) {
                            if (btn.offsetParent && !btn.disabled) {
                                const btnText = btn.textContent.toLowerCase().trim();
                                const hasKeyword = expandKeywords.some(keyword =>
                                    btnText.includes(keyword.toLowerCase())
                                );

                                if (hasKeyword) {
                                    try {
                                        btn.click();
                                        console.log(`📖 预展开: ${btn.textContent.trim()}`);
                                        totalExpanded++;
                                        await new Promise(resolve => setTimeout(resolve, 300));
                                    } catch (e) {
                                        // 忽略点击错误
                                    }
                                }
                            }
                        }
                    } catch (e) {
                        // 忽略选择器错误
                    }
                }

                console.log(`📖 预展开完成: ${totalExpanded} 个内容`);
                return totalExpanded;
            })();
            """,

            # 使用配置化的滚动策略
            ScrollConfig.generate_scroll_js(target_count=count, strategy=scroll_strategy)
        ]

        # 使用增强的爬取配置 - 针对滚动优化
        crawl_result = await xueqiu_crawler._crawl_with_crawl4ai(
            url,
            js_code=enhanced_js_code,
            page_timeout=35000,  # 35秒超时
            delay_before_return_html=8,  # 等待8秒确保滚动完成
            # 移除networkidle等待，使用固定延迟更稳定
        )

        if crawl_result.success:
            # 使用增强的HTML解析
            comments = self._extract_comments_from_enhanced_html(crawl_result.data)

            # 限制评论数量
            if count and len(comments) > count:
                comments = comments[:count]

            # 验证并标准化评论数据结构
            validated_comments = self._validate_comment_structure(comments)

            return CrawlResult(
                success=True,
                data=validated_comments,
                raw_html=crawl_result.data,
                url=url,
                response_time=0,
                data_type="comments"
            )
        else:
            return CrawlResult(
                success=False,
                error=crawl_result.error,
                url=url,
                response_time=0,
                data_type="comments"
            )

    async def crawl_comprehensive_data(self, symbol: str) -> Dict[str, Any]:
        """综合爬取个股所有数据"""
        logger.info(f"开始综合爬取股票数据: {symbol}")

        results = await self.crawler.crawl_comprehensive_stock_data(symbol)

        formatted_results = {}
        for data_type, result in results.items():
            if result.success:
                if data_type == 'basic':
                    formatted_results[data_type] = self._format_result(result)
                elif data_type == 'detail':
                    formatted_results[data_type] = self._format_detail_result(result)
                elif data_type == 'comments':
                    formatted_results[data_type] = self._format_comments_result(result, symbol)
            else:
                formatted_results[data_type] = {"success": False, "error": result.error}

        return {
            "symbol": symbol,
            "success": True,
            "data": formatted_results,
            "summary": self._create_comprehensive_summary(formatted_results)
        }

    def _format_homepage_result(self, result: CrawlResult) -> Dict[str, Any]:
        """格式化首页结果"""
        data = result.data
        return {
            "success": True,
            "data_type": "homepage",
            "hot_stocks_count": len(data.hot_stocks) if data.hot_stocks else 0,
            "hot_topics_count": len(data.hot_topics) if data.hot_topics else 0,
            "user_posts_count": len(data.user_posts) if data.user_posts else 0,
            "hot_stocks": [
                {
                    "symbol": stock.symbol,
                    "name": stock.name,
                    "current_price": stock.current_price,
                    "change_percent": stock.change_percent,
                    "rank": stock.rank
                } for stock in (data.hot_stocks[:5] if data.hot_stocks else [])
            ],
            "hot_topics": [
                {
                    "title": topic.title,
                    "author": topic.author,
                    "view_count": topic.view_count,
                    "comment_count": topic.comment_count,
                    "url": topic.url
                } for topic in (data.hot_topics[:5] if data.hot_topics else [])
            ],
            "user_posts": [
                {
                    "author": post.author,
                    "content": post.content[:200] + "..." if len(post.content) > 200 else post.content,
                    "publish_time": post.publish_time,
                    "like_count": post.like_count,
                    "comment_count": post.comment_count,
                    "mentioned_stocks": post.mentioned_stocks,
                    "is_repost": post.is_repost,
                    "images_count": len(post.images) if post.images else 0
                } for post in (data.user_posts[:5] if data.user_posts else [])
            ],
            "market_summary": data.market_summary,
            "crawl_time": data.crawl_time,
            "response_time": result.response_time
        }

    def _format_detail_result(self, result: CrawlResult) -> Dict[str, Any]:
        """格式化详细信息结果"""
        data = result.data
        return {
            "success": True,
            "data_type": "detail",
            "basic_info": self._format_result(CrawlResult(
                success=True,
                data=data.basic_info,
                response_time=result.response_time
            )) if data.basic_info else None,
            "comments_count": len(data.comments) if data.comments else 0,
            "news_count": len(data.news) if data.news else 0,
            "financial_data": data.financial_data,
            "technical_indicators": data.technical_indicators,
            "response_time": result.response_time
        }

    def _format_comments_result(self, result: CrawlResult, symbol: str) -> Dict[str, Any]:
        """格式化评论结果 - 确保与API格式一致"""
        comments = result.data

        # 构造与雪球API相同的响应格式
        api_format_response = {
            "count": len(comments),
            "maxPage": 1,
            "page": 1,
            "list": comments
        }

        return {
            "success": True,
            "symbol": symbol,
            "data_type": "comments",
            "total_comments": len(comments),
            "response_time": result.response_time,
            "api_format": api_format_response,
            "preview": [
                {
                    "id": comment.get('id', ''),
                    "text": comment.get('text', '')[:200] + "..." if len(comment.get('text', '')) > 200 else comment.get('text', ''),
                    "author": comment.get('user', {}).get('screen_name', ''),
                    "created_at": comment.get('created_at', ''),
                    "fav_count": comment.get('fav_count', 0),
                    "reply_count": comment.get('reply_count', 0)
                } for comment in comments[:10]  # 只显示前10条预览
            ]
        }

    def _validate_comment_structure(self, comments: List) -> List[Dict[str, Any]]:
        """验证评论数据结构，确保与API格式一致"""
        validated = []

        for comment in comments:
            try:
                # 如果已经是字典格式，直接使用
                if isinstance(comment, dict):
                    validated.append(comment)
                # 如果是对象格式，转换为字典
                elif hasattr(comment, 'content'):
                    validated_comment = {
                        'id': getattr(comment, 'comment_id', ''),
                        'text': getattr(comment, 'content', ''),
                        'user': {
                            'screen_name': getattr(comment, 'author', ''),
                            'id': getattr(comment, 'author_id', '')
                        },
                        'created_at': getattr(comment, 'publish_time', ''),
                        'fav_count': getattr(comment, 'like_count', 0) or 0,
                        'reply_count': getattr(comment, 'reply_count', 0) or 0
                    }
                    validated.append(validated_comment)

            except Exception as e:
                logger.debug(f"验证评论数据失败: {e}")
                continue

        return validated

    def _extract_comments_from_enhanced_html(self, html_content: str) -> List[Dict[str, Any]]:
        """从增强的HTML中提取评论 - 带去重功能"""
        comments = []
        processed_elements = set()  # 用于跟踪已处理的元素

        try:
            from bs4 import BeautifulSoup
            soup = BeautifulSoup(html_content, 'html.parser')

            # 按优先级排序的选择器策略
            selectors = [
                '.timeline__item__comment',  # 最具体的评论选择器
                '.status-item',
                '.comment-item',
                '.feed-item',
                '[class*="timeline"]',
                '[class*="status"]',
                '[class*="comment"]',
                '[data-id]'
            ]

            for selector in selectors:
                elements = soup.select(selector)
                if elements:
                    logger.info(f"找到 {len(elements)} 个元素: {selector}")
                    new_comments_count = 0

                    for i, element in enumerate(elements):
                        # 使用元素的唯一标识符避免重复处理
                        element_id = self._get_element_identifier(element)

                        if element_id in processed_elements:
                            continue  # 跳过已处理的元素

                        processed_elements.add(element_id)

                        comment = self._extract_comment_from_element(element, len(comments))
                        if comment:
                            # 检查内容是否重复
                            if not self._is_duplicate_comment(comment, comments):
                                comments.append(comment)
                                new_comments_count += 1

                    logger.info(f"选择器 {selector} 新增 {new_comments_count} 条有效评论")

                    # 如果找到足够的评论，可以提前结束
                    if len(comments) >= 50:  # 设置一个合理的上限
                        break

        except Exception as e:
            logger.error(f"HTML解析失败: {e}")

        # 最终去重和排序
        unique_comments = self._deduplicate_comments(comments)
        logger.info(f"去重后评论数: {len(unique_comments)} (原始: {len(comments)})")

        return unique_comments

    def _get_element_identifier(self, element) -> str:
        """获取元素的唯一标识符"""
        # 尝试多种方式生成唯一标识
        identifiers = []

        # 1. 使用data-id属性
        if element.get('data-id'):
            identifiers.append(f"data-id:{element.get('data-id')}")

        # 2. 使用id属性
        if element.get('id'):
            identifiers.append(f"id:{element.get('id')}")

        # 3. 使用class和文本内容的组合
        classes = ' '.join(element.get('class', []))
        text = element.get_text(strip=True)[:50]  # 取前50个字符
        if classes and text:
            identifiers.append(f"class-text:{classes}:{text}")

        # 4. 使用元素在DOM中的位置
        parent = element.parent
        if parent:
            siblings = parent.find_all(element.name)
            try:
                index = siblings.index(element)
                identifiers.append(f"position:{parent.name}:{index}")
            except ValueError:
                pass

        # 返回最具体的标识符，如果没有则使用文本内容
        return identifiers[0] if identifiers else f"text:{text}"

    def _is_duplicate_comment(self, new_comment: Dict[str, Any], existing_comments: List[Dict[str, Any]]) -> bool:
        """检查评论是否重复"""
        new_text = new_comment.get('text', '').strip()
        new_author = new_comment.get('user', {}).get('screen_name', '').strip()

        if not new_text or len(new_text) < 10:
            return True  # 过滤过短的内容

        for existing in existing_comments:
            existing_text = existing.get('text', '').strip()
            existing_author = existing.get('user', {}).get('screen_name', '').strip()

            # 检查文本内容是否相同
            if new_text == existing_text:
                return True

            # 检查是否是相同作者的相似内容
            if new_author == existing_author and new_author:
                # 计算文本相似度
                similarity = self._calculate_text_similarity(new_text, existing_text)
                if similarity > 0.8:  # 80%相似度认为是重复
                    return True

        return False

    def _calculate_text_similarity(self, text1: str, text2: str) -> float:
        """计算两个文本的相似度"""
        if not text1 or not text2:
            return 0.0

        # 简单的字符级相似度计算
        set1 = set(text1)
        set2 = set(text2)

        intersection = len(set1.intersection(set2))
        union = len(set1.union(set2))

        return intersection / union if union > 0 else 0.0

    def _deduplicate_comments(self, comments: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """最终去重处理"""
        unique_comments = []
        seen_texts = set()
        seen_combinations = set()

        for comment in comments:
            text = comment.get('text', '').strip()
            author = comment.get('user', {}).get('screen_name', '').strip()

            # 跳过空内容
            if not text or len(text) < 10:
                continue

            # 跳过重复文本
            if text in seen_texts:
                continue

            # 跳过相同作者的相同内容
            combination = f"{author}:{text}"
            if combination in seen_combinations:
                continue

            seen_texts.add(text)
            seen_combinations.add(combination)
            unique_comments.append(comment)

        return unique_comments

    def _extract_comment_from_element(self, element, index: int) -> Dict[str, Any]:
        """从DOM元素提取评论"""
        try:
            # 提取文本内容
            text_selectors = ['.text', '.content', '.desc', 'p', '.message']
            text = ""
            for sel in text_selectors:
                text_elem = element.select_one(sel)
                if text_elem:
                    text = text_elem.get_text(strip=True)
                    break

            if not text:
                text = element.get_text(strip=True)

            # 过滤无效评论
            if self._is_invalid_comment(text):
                return None

            # 提取作者
            author_selectors = ['.user-name', '.author', '.username', 'a[href*="/u/"]']
            author = ""
            author_id = ""

            for sel in author_selectors:
                author_elem = element.select_one(sel)
                if author_elem:
                    author = author_elem.get_text(strip=True)
                    # 尝试提取用户ID
                    href = author_elem.get('href', '')
                    if '/u/' in href:
                        import re
                        match = re.search(r'/u/(\d+)', href)
                        if match:
                            author_id = match.group(1)
                    break

            # 如果没有找到有效作者，可能不是评论
            if not author or author in ['全部', '新帖', '热帖', '讨论', '交易', '资讯', '公告']:
                return None

            # 提取时间信息
            time_elem = element.select_one('[class*="time"], .timestamp, time')
            created_at = time_elem.get_text(strip=True) if time_elem else '2024-01-15T10:00:00'

            # 提取数字信息
            like_count = self._extract_number_from_element(element, ['like', 'fav', 'praise'])
            reply_count = self._extract_number_from_element(element, ['reply', 'comment'])

            return {
                'id': str(index + 1000),
                'text': text,
                'user': {
                    'screen_name': author,
                    'id': author_id or str(index + 2000)
                },
                'created_at': created_at,
                'fav_count': like_count,
                'reply_count': reply_count
            }

        except Exception as e:
            logger.debug(f"元素解析失败: {e}")
            return None

    def _is_invalid_comment(self, text: str) -> bool:
        """判断是否为无效评论"""
        if len(text) < 10:  # 太短的文本
            return True

        # 过滤导航和标签文本
        invalid_patterns = [
            '全部讨论交易资讯公告',
            '新帖热帖',
            '全部',
            '讨论',
            '交易',
            '资讯',
            '公告',
            '新帖',
            '热帖',
            '加载更多',
            '查看更多',
            '展开',
            '收起'
        ]

        for pattern in invalid_patterns:
            if pattern in text:
                return True

        # 如果文本主要是标点符号或数字
        import re
        if re.match(r'^[\s\d\.\-\+%]+$', text):
            return True

        return False

    def _extract_number_from_element(self, element, keywords: List[str]) -> int:
        """从元素中提取数字"""
        for keyword in keywords:
            for sel in [f'[class*="{keyword}"]', f'.{keyword}']:
                elem = element.select_one(sel)
                if elem:
                    text = elem.get_text(strip=True)
                    import re
                    numbers = re.findall(r'\d+', text)
                    if numbers:
                        return int(numbers[0])
        return 0

    def _create_comprehensive_summary(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """创建综合数据摘要"""
        summary = {
            "data_types_collected": list(results.keys()),
            "success_count": sum(1 for r in results.values() if r.get('success')),
            "total_count": len(results)
        }

        # 添加具体数据统计
        if 'detail' in results and results['detail'].get('success'):
            detail = results['detail']
            summary['comments_count'] = detail.get('comments_count', 0)
            summary['news_count'] = detail.get('news_count', 0)

        if 'comments' in results and results['comments'].get('success'):
            summary['total_comments'] = results['comments'].get('total_comments', 0)

        return summary

async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='雪球股票数据爬虫')
    parser.add_argument('--symbols', nargs='+', default=['SH688627'], 
                       help='股票代码列表')
    parser.add_argument('--concurrent', type=int, default=3,
                       help='并发数量')
    parser.add_argument('--output', type=str, 
                       help='输出文件名')
    parser.add_argument('--disable-anti-detection', action='store_true',
                       help='禁用反检测功能')
    parser.add_argument('--test-mode', action='store_true',
                       help='测试模式，只爬取一个股票')
    parser.add_argument('--mode', choices=['stock', 'homepage', 'detail', 'comments', 'comprehensive'],
                       default='stock', help='爬取模式')
    parser.add_argument('--comment-count', type=int, default=10,
                       help='爬取评论数量')
    parser.add_argument('--scroll-strategy', choices=['conservative', 'moderate', 'aggressive'],
                       default='moderate', help='滚动策略：conservative(保守), moderate(适中), aggressive(激进)')
    parser.add_argument('--enhanced', action='store_true',
                       help='启用增强模式（自动展开和滚动加载）')

    args = parser.parse_args()
    
    # 创建爬虫应用
    app = CrawlerApp(enable_anti_detection=not args.disable_anti_detection)
    
    try:
        if args.mode == 'homepage':
            # 首页爬取模式
            if args.enhanced:
                logger.info("运行增强版首页爬取模式（支持展开和滚动）")
                result = await app.crawl_homepage_enhanced()
            else:
                logger.info("运行标准首页爬取模式")
                result = await app.crawl_homepage()
            print(json.dumps(result, ensure_ascii=False, indent=2))
            if args.output:
                app.save_results([result], args.output)

        elif args.mode == 'detail':
            # 详细信息爬取模式
            logger.info("运行详细信息爬取模式")
            results = []
            for symbol in args.symbols:
                result = await app.crawl_stock_detail(symbol)
                results.append(result)
                print(f"详细信息爬取完成: {symbol}")

            if args.output:
                app.save_results(results, args.output)

            # 打印摘要
            for result in results:
                if result.get('success'):
                    print(f"✓ {result.get('symbol', 'Unknown')}: 详细信息已获取")
                else:
                    print(f"✗ {result.get('symbol', 'Unknown')}: {result.get('error')}")

        elif args.mode == 'comments':
            # 评论爬取模式
            logger.info("运行评论爬取模式")
            results = []
            for symbol in args.symbols:
                result = await app.crawl_stock_comments(symbol, args.comment_count, args.scroll_strategy)
                results.append(result)
                print(f"评论爬取完成: {symbol}")

            if args.output:
                app.save_results(results, args.output)

            # 打印摘要
            for result in results:
                if result.get('success'):
                    print(f"✓ {result.get('symbol', 'Unknown')}: 获取 {result.get('total_comments', 0)} 条评论")
                else:
                    print(f"✗ {result.get('symbol', 'Unknown')}: {result.get('error')}")

        elif args.mode == 'comprehensive':
            # 综合爬取模式
            logger.info("运行综合爬取模式")
            results = []
            for symbol in args.symbols:
                result = await app.crawl_comprehensive_data(symbol)
                results.append(result)
                print(f"综合数据爬取完成: {symbol}")

            if args.output:
                app.save_results(results, args.output)

            # 打印摘要
            for result in results:
                if result.get('success'):
                    summary = result.get('summary', {})
                    print(f"✓ {result.get('symbol', 'Unknown')}: 收集 {summary.get('success_count', 0)}/{summary.get('total_count', 0)} 类数据")
                else:
                    print(f"✗ {result.get('symbol', 'Unknown')}: 综合爬取失败")

        elif args.test_mode:
            # 测试模式
            logger.info("运行测试模式")
            result = await app.crawl_single_stock(args.symbols[0])
            print(json.dumps(result, ensure_ascii=False, indent=2))
        else:
            # 默认股票爬取模式
            results = await app.crawl_multiple_stocks(
                args.symbols,
                concurrent_limit=args.concurrent
            )

            # 打印结果摘要
            successful_count = sum(1 for r in results if r.get('success'))
            logger.info(f"爬取完成: {successful_count}/{len(results)} 成功")

            # 保存结果
            if args.output:
                app.save_results(results, args.output)

            # 打印部分结果
            for result in results[:5]:  # 只打印前5个结果
                if result.get('success'):
                    print(f"{result['symbol']} - {result['name']}: ¥{result['current_price']} ({result['change_percent']:+.2f}%)")
                else:
                    print(f"{result.get('symbol', 'Unknown')}: 爬取失败 - {result.get('error')}")
                    
    except KeyboardInterrupt:
        logger.info("用户中断爬取")
    except Exception as e:
        logger.error(f"爬取过程中发生错误: {e}")
        raise

# 预定义的股票列表
POPULAR_STOCKS = [
    'SH688627',  # 精智达
    'SZ000001',  # 平安银行
    'SH600036',  # 招商银行
    'SZ000002',  # 万科A
    'SH600519',  # 贵州茅台
    'SZ000858',  # 五粮液
    'SH600276',  # 恒瑞医药
    'SZ002415',  # 海康威视
    'SH600887',  # 伊利股份
    'SZ000725'   # 京东方A
]

async def demo_crawl():
    """演示爬取 - 展示所有新功能"""
    print("=== 雪球股票爬虫升级版演示 ===")

    app = CrawlerApp(enable_anti_detection=True)

    # 1. 演示首页爬取
    print("\n1. 首页热门信息爬取演示")
    print("-" * 40)
    homepage_result = await app.crawl_homepage()
    if homepage_result.get('success'):
        print(f"✓ 首页数据爬取成功")
        print(f"  热门股票: {homepage_result.get('hot_stocks_count', 0)} 个")
        print(f"  热门话题: {homepage_result.get('hot_topics_count', 0)} 个")

        # 显示部分热门股票
        hot_stocks = homepage_result.get('hot_stocks', [])
        if hot_stocks:
            print("  前3个热门股票:")
            for stock in hot_stocks[:3]:
                print(f"    {stock['symbol']} - {stock['name']}: {stock.get('change_percent', 'N/A')}%")
    else:
        print(f"✗ 首页爬取失败: {homepage_result.get('error')}")

    # 2. 演示个股详细信息爬取
    print("\n2. 个股详细信息爬取演示")
    print("-" * 40)
    demo_symbol = POPULAR_STOCKS[0]
    detail_result = await app.crawl_stock_detail(demo_symbol)
    if detail_result.get('success'):
        print(f"✓ {demo_symbol} 详细信息爬取成功")
        print(f"  评论数量: {detail_result.get('comments_count', 0)}")
        print(f"  新闻数量: {detail_result.get('news_count', 0)}")

        basic_info = detail_result.get('basic_info')
        if basic_info and basic_info.get('success'):
            print(f"  股票名称: {basic_info.get('name', 'N/A')}")
            print(f"  当前价格: ¥{basic_info.get('current_price', 'N/A')}")
    else:
        print(f"✗ {demo_symbol} 详细信息爬取失败: {detail_result.get('error')}")

    # 3. 演示评论爬取
    print("\n3. 个股评论爬取演示")
    print("-" * 40)
    comments_result = await app.crawl_stock_comments(demo_symbol, count=5, scroll_strategy="moderate")
    if comments_result.get('success'):
        print(f"✓ {demo_symbol} 评论爬取成功")
        print(f"  总评论数: {comments_result.get('total_comments', 0)}")

        comments = comments_result.get('comments', [])
        if comments:
            print("  最新评论预览:")
            for i, comment in enumerate(comments[:2], 1):
                content = comment['content'][:50] + "..." if len(comment['content']) > 50 else comment['content']
                print(f"    {i}. {comment['author']}: {content}")
    else:
        print(f"✗ {demo_symbol} 评论爬取失败: {comments_result.get('error')}")

    # 4. 演示综合爬取
    print("\n4. 综合数据爬取演示")
    print("-" * 40)
    comprehensive_result = await app.crawl_comprehensive_data(demo_symbol)
    if comprehensive_result.get('success'):
        print(f"✓ {demo_symbol} 综合数据爬取成功")
        summary = comprehensive_result.get('summary', {})
        print(f"  数据类型: {', '.join(summary.get('data_types_collected', []))}")
        print(f"  成功率: {summary.get('success_count', 0)}/{summary.get('total_count', 0)}")
    else:
        print(f"✗ {demo_symbol} 综合爬取失败")

    # 性能报告
    print("\n=== 性能报告 ===")
    report = app.crawler.get_performance_report()
    for key, value in report.items():
        print(f"{key}: {value}")

    print("\n=== 使用说明 ===")
    print("命令行使用示例:")
    print("  python main_advanced.py --mode homepage                    # 爬取首页")
    print("  python main_advanced.py --mode detail --symbols SH688627  # 爬取个股详情")
    print("  python main_advanced.py --mode comments --symbols SH688627 # 爬取评论")
    print("  python main_advanced.py --mode comprehensive --symbols SH688627 # 综合爬取")

if __name__ == "__main__":
    # 如果没有命令行参数，运行演示
    import sys
    if len(sys.argv) == 1:
        asyncio.run(demo_crawl())
    else:
        asyncio.run(main())

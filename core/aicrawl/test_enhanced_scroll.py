#!/usr/bin/env python3
"""
测试增强滚动功能
验证不同滚动策略的效果
"""
import asyncio
import logging
import json
from datetime import datetime
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_scroll_strategies():
    """测试不同的滚动策略"""
    print("🧪 测试增强滚动功能")
    print("=" * 50)
    
    from main_advanced import AdvancedCrawlerApp
    
    # 创建爬虫应用
    app = AdvancedCrawlerApp(enable_anti_detection=True)
    
    # 测试股票
    test_symbol = "SH688775"  # 影石创新
    target_count = 15
    
    # 测试不同的滚动策略
    strategies = ["conservative", "moderate", "aggressive"]
    results = {}
    
    for strategy in strategies:
        print(f"\n📊 测试滚动策略: {strategy}")
        print("-" * 30)
        
        try:
            start_time = datetime.now()
            result = await app.crawl_stock_comments(
                symbol=test_symbol, 
                count=target_count, 
                scroll_strategy=strategy
            )
            end_time = datetime.now()
            
            duration = (end_time - start_time).total_seconds()
            
            if result.get('success'):
                comment_count = result.get('total_comments', 0)
                print(f"✅ 成功: 获取 {comment_count} 条评论")
                print(f"⏱️  耗时: {duration:.1f} 秒")
                
                # 分析评论质量
                api_data = result.get('api_format', {})
                comments = api_data.get('list', [])
                
                if comments:
                    # 统计作者数量
                    authors = set(c.get('user', {}).get('screen_name', '') for c in comments)
                    unique_authors = len([a for a in authors if a])
                    
                    # 统计互动数据
                    total_likes = sum(c.get('fav_count', 0) for c in comments)
                    avg_likes = total_likes / len(comments) if comments else 0
                    
                    print(f"👥 独立用户: {unique_authors}")
                    print(f"👍 总点赞数: {total_likes}")
                    print(f"📊 平均点赞: {avg_likes:.1f}")
                    
                    # 显示前3条评论
                    print(f"💬 评论示例:")
                    for i, comment in enumerate(comments[:3], 1):
                        author = comment.get('user', {}).get('screen_name', '未知')
                        content = comment.get('text', '')[:50] + "..."
                        likes = comment.get('fav_count', 0)
                        print(f"   {i}. 【{author}】{content} (👍 {likes})")
                
                results[strategy] = {
                    "success": True,
                    "comment_count": comment_count,
                    "duration": duration,
                    "unique_authors": unique_authors if 'unique_authors' in locals() else 0,
                    "total_likes": total_likes if 'total_likes' in locals() else 0,
                    "avg_likes": avg_likes if 'avg_likes' in locals() else 0
                }
            else:
                print(f"❌ 失败: {result.get('error', '未知错误')}")
                results[strategy] = {
                    "success": False,
                    "error": result.get('error', '未知错误'),
                    "duration": duration
                }
                
        except Exception as e:
            print(f"❌ 异常: {e}")
            results[strategy] = {
                "success": False,
                "error": str(e),
                "duration": 0
            }
        
        # 添加延迟避免请求过快
        if strategy != strategies[-1]:
            print("⏳ 等待5秒...")
            await asyncio.sleep(5)
    
    # 保存测试结果
    print(f"\n📁 保存测试结果")
    print("-" * 30)
    
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    filename = f"scroll_test_results_{timestamp}.json"
    
    test_summary = {
        "test_info": {
            "symbol": test_symbol,
            "target_count": target_count,
            "test_time": datetime.now().isoformat(),
            "strategies_tested": strategies
        },
        "results": results
    }
    
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(test_summary, f, ensure_ascii=False, indent=2)
    
    print(f"✅ 结果已保存: {filename}")
    
    # 分析和比较结果
    print(f"\n📊 策略对比分析")
    print("-" * 30)
    
    successful_results = {k: v for k, v in results.items() if v.get('success')}
    
    if successful_results:
        print(f"{'策略':<12} {'评论数':<8} {'耗时(秒)':<10} {'用户数':<8} {'平均点赞':<10}")
        print("-" * 60)
        
        for strategy, data in successful_results.items():
            print(f"{strategy:<12} {data['comment_count']:<8} {data['duration']:<10.1f} "
                  f"{data['unique_authors']:<8} {data['avg_likes']:<10.1f}")
        
        # 推荐最佳策略
        best_strategy = max(successful_results.items(), 
                          key=lambda x: x[1]['comment_count'])
        
        print(f"\n🏆 推荐策略: {best_strategy[0]}")
        print(f"   理由: 获取了最多的评论数 ({best_strategy[1]['comment_count']} 条)")
        
        # 效率分析
        efficiency_scores = {}
        for strategy, data in successful_results.items():
            # 效率 = 评论数 / 耗时
            efficiency = data['comment_count'] / data['duration'] if data['duration'] > 0 else 0
            efficiency_scores[strategy] = efficiency
        
        most_efficient = max(efficiency_scores.items(), key=lambda x: x[1])
        print(f"\n⚡ 最高效策略: {most_efficient[0]}")
        print(f"   效率: {most_efficient[1]:.2f} 评论/秒")
    
    else:
        print("❌ 所有策略都失败了")
    
    return results

async def test_single_strategy():
    """测试单个策略的详细效果"""
    print(f"\n🔍 单策略详细测试")
    print("-" * 30)
    
    from main_advanced import AdvancedCrawlerApp
    
    app = AdvancedCrawlerApp(enable_anti_detection=True)
    
    # 使用moderate策略进行详细测试
    result = await app.crawl_stock_comments(
        symbol="SH688521", 
        count=20, 
        scroll_strategy="moderate"
    )
    
    if result.get('success'):
        api_data = result.get('api_format', {})
        comments = api_data.get('list', [])
        
        print(f"✅ 获取评论: {len(comments)} 条")
        
        # 详细分析评论内容
        if comments:
            print(f"\n📝 评论内容分析:")
            
            # 按长度分类
            short_comments = [c for c in comments if len(c.get('text', '')) < 50]
            medium_comments = [c for c in comments if 50 <= len(c.get('text', '')) < 150]
            long_comments = [c for c in comments if len(c.get('text', '')) >= 150]
            
            print(f"   短评论 (<50字): {len(short_comments)} 条")
            print(f"   中等评论 (50-150字): {len(medium_comments)} 条") 
            print(f"   长评论 (>=150字): {len(long_comments)} 条")
            
            # 互动数据分析
            likes_data = [c.get('fav_count', 0) for c in comments]
            if likes_data:
                print(f"\n👍 点赞数据分析:")
                print(f"   最高点赞: {max(likes_data)}")
                print(f"   平均点赞: {sum(likes_data)/len(likes_data):.1f}")
                print(f"   有点赞的评论: {len([l for l in likes_data if l > 0])} 条")
        
        return True
    else:
        print(f"❌ 测试失败: {result.get('error')}")
        return False

async def main():
    """主测试函数"""
    print("🚀 增强滚动功能测试")
    print("=" * 60)
    print("测试不同滚动策略获取更多评论的效果")
    print()
    
    # 测试1: 策略对比
    await test_scroll_strategies()
    
    # 测试2: 单策略详细测试
    await test_single_strategy()
    
    print(f"\n🎉 测试完成!")
    print("💡 使用建议:")
    print("   - conservative: 适合网络较慢或需要稳定性的场景")
    print("   - moderate: 平衡效果和速度，推荐日常使用")
    print("   - aggressive: 追求最大评论数量，适合数据收集")

if __name__ == "__main__":
    asyncio.run(main())

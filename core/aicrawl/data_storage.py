"""
数据存储模块
负责爬取数据的存储、去重和管理
"""
import asyncio
import logging
import json
import hashlib
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
import sqlite3
import aiosqlite
from pathlib import Path

logger = logging.getLogger(__name__)

@dataclass
class CrawlTask:
    """爬取任务数据结构"""
    task_id: str
    task_type: str
    target: str
    status: str
    created_at: datetime
    completed_at: Optional[datetime] = None
    error_message: Optional[str] = None

@dataclass
class CrawlResult:
    """爬取结果数据结构"""
    result_id: str
    task_id: str
    data_type: str
    content: Dict[str, Any]
    crawl_time: datetime
    data_hash: str

class DataStorage:
    """数据存储管理器"""
    
    def __init__(self, db_path: str = "xueqiu_crawl_data.db"):
        self.db_path = Path(db_path)
        self.connection = None
        
    async def initialize(self):
        """初始化数据库"""
        logger.info("初始化数据存储系统...")
        
        # 创建数据库连接
        self.connection = await aiosqlite.connect(self.db_path)
        
        # 创建表结构
        await self._create_tables()
        
        logger.info("数据存储系统初始化完成")

    async def _create_tables(self):
        """创建数据库表"""
        # 爬取任务表
        await self.connection.execute("""
            CREATE TABLE IF NOT EXISTS crawl_tasks (
                task_id TEXT PRIMARY KEY,
                task_type TEXT NOT NULL,
                target TEXT NOT NULL,
                status TEXT NOT NULL,
                created_at TIMESTAMP NOT NULL,
                completed_at TIMESTAMP,
                error_message TEXT
            )
        """)
        
        # 爬取结果表
        await self.connection.execute("""
            CREATE TABLE IF NOT EXISTS crawl_results (
                result_id TEXT PRIMARY KEY,
                task_id TEXT NOT NULL,
                data_type TEXT NOT NULL,
                content TEXT NOT NULL,
                crawl_time TIMESTAMP NOT NULL,
                data_hash TEXT NOT NULL,
                FOREIGN KEY (task_id) REFERENCES crawl_tasks (task_id)
            )
        """)
        
        # 热门股票表
        await self.connection.execute("""
            CREATE TABLE IF NOT EXISTS hot_stocks (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                name TEXT,
                current_price REAL,
                change_percent REAL,
                rank INTEGER,
                volume REAL,
                market_cap REAL,
                crawl_time TIMESTAMP NOT NULL,
                data_hash TEXT UNIQUE
            )
        """)
        
        # 热门话题表
        await self.connection.execute("""
            CREATE TABLE IF NOT EXISTS hot_topics (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                title TEXT NOT NULL,
                author TEXT,
                view_count INTEGER,
                comment_count INTEGER,
                url TEXT,
                publish_time TEXT,
                crawl_time TIMESTAMP NOT NULL,
                data_hash TEXT UNIQUE
            )
        """)
        
        # 用户动态表
        await self.connection.execute("""
            CREATE TABLE IF NOT EXISTS user_posts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                author TEXT NOT NULL,
                content TEXT NOT NULL,
                publish_time TEXT,
                like_count INTEGER,
                comment_count INTEGER,
                mentioned_stocks TEXT,
                is_repost BOOLEAN,
                images_count INTEGER,
                url TEXT,
                crawl_time TIMESTAMP NOT NULL,
                data_hash TEXT UNIQUE
            )
        """)
        
        # 股票评论表
        await self.connection.execute("""
            CREATE TABLE IF NOT EXISTS stock_comments (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                comment_id TEXT,
                stock_code TEXT NOT NULL,
                text TEXT NOT NULL,
                author TEXT,
                author_id TEXT,
                created_at TEXT,
                like_count INTEGER,
                reply_count INTEGER,
                sentiment TEXT,
                mentioned_stocks TEXT,
                crawl_time TIMESTAMP NOT NULL,
                data_hash TEXT UNIQUE
            )
        """)
        
        # 热门股票列表表
        await self.connection.execute("""
            CREATE TABLE IF NOT EXISTS trending_stocks (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                stock_code TEXT NOT NULL,
                rank INTEGER,
                update_time TIMESTAMP NOT NULL,
                UNIQUE(stock_code, update_time)
            )
        """)
        
        # 创建索引
        await self._create_indexes()
        
        await self.connection.commit()

    async def _create_indexes(self):
        """创建数据库索引"""
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_crawl_tasks_type ON crawl_tasks(task_type)",
            "CREATE INDEX IF NOT EXISTS idx_crawl_tasks_status ON crawl_tasks(status)",
            "CREATE INDEX IF NOT EXISTS idx_crawl_results_type ON crawl_results(data_type)",
            "CREATE INDEX IF NOT EXISTS idx_crawl_results_time ON crawl_results(crawl_time)",
            "CREATE INDEX IF NOT EXISTS idx_hot_stocks_symbol ON hot_stocks(symbol)",
            "CREATE INDEX IF NOT EXISTS idx_hot_stocks_time ON hot_stocks(crawl_time)",
            "CREATE INDEX IF NOT EXISTS idx_stock_comments_code ON stock_comments(stock_code)",
            "CREATE INDEX IF NOT EXISTS idx_stock_comments_time ON stock_comments(crawl_time)",
            "CREATE INDEX IF NOT EXISTS idx_user_posts_author ON user_posts(author)",
            "CREATE INDEX IF NOT EXISTS idx_user_posts_time ON user_posts(crawl_time)",
            "CREATE INDEX IF NOT EXISTS idx_trending_stocks_code ON trending_stocks(stock_code)",
            "CREATE INDEX IF NOT EXISTS idx_trending_stocks_time ON trending_stocks(update_time)"
        ]
        
        for index_sql in indexes:
            await self.connection.execute(index_sql)

    def _generate_data_hash(self, data: Dict[str, Any]) -> str:
        """生成数据哈希值用于去重"""
        # 将数据转换为JSON字符串并生成哈希
        data_str = json.dumps(data, sort_keys=True, ensure_ascii=False)
        return hashlib.md5(data_str.encode('utf-8')).hexdigest()

    async def save_crawl_result(self, task_id: str, task_type: str, target: str, 
                               result: Dict[str, Any], crawl_time: datetime):
        """保存爬取结果"""
        try:
            result_id = f"{task_id}_{int(crawl_time.timestamp())}"
            data_hash = self._generate_data_hash(result)
            
            await self.connection.execute("""
                INSERT OR REPLACE INTO crawl_results 
                (result_id, task_id, data_type, content, crawl_time, data_hash)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (result_id, task_id, task_type, json.dumps(result), crawl_time, data_hash))
            
            await self.connection.commit()
            logger.debug(f"爬取结果已保存: {result_id}")
            
        except Exception as e:
            logger.error(f"保存爬取结果失败: {e}")
            raise

    async def save_hot_stock(self, stock_data: Dict[str, Any]):
        """保存热门股票数据"""
        try:
            data_hash = self._generate_data_hash(stock_data)
            
            await self.connection.execute("""
                INSERT OR IGNORE INTO hot_stocks 
                (symbol, name, current_price, change_percent, rank, volume, market_cap, crawl_time, data_hash)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                stock_data.get('symbol'),
                stock_data.get('name'),
                stock_data.get('current_price'),
                stock_data.get('change_percent'),
                stock_data.get('rank'),
                stock_data.get('volume'),
                stock_data.get('market_cap'),
                datetime.now(),
                data_hash
            ))
            
            await self.connection.commit()
            
        except Exception as e:
            logger.error(f"保存热门股票数据失败: {e}")

    async def save_hot_topic(self, topic_data: Dict[str, Any]):
        """保存热门话题数据"""
        try:
            data_hash = self._generate_data_hash(topic_data)
            
            await self.connection.execute("""
                INSERT OR IGNORE INTO hot_topics 
                (title, author, view_count, comment_count, url, publish_time, crawl_time, data_hash)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                topic_data.get('title'),
                topic_data.get('author'),
                topic_data.get('view_count'),
                topic_data.get('comment_count'),
                topic_data.get('url'),
                topic_data.get('publish_time'),
                datetime.now(),
                data_hash
            ))
            
            await self.connection.commit()
            
        except Exception as e:
            logger.error(f"保存热门话题数据失败: {e}")

    async def save_user_post(self, post_data: Dict[str, Any]):
        """保存用户动态数据"""
        try:
            data_hash = self._generate_data_hash(post_data)
            
            await self.connection.execute("""
                INSERT OR IGNORE INTO user_posts 
                (author, content, publish_time, like_count, comment_count, mentioned_stocks, 
                 is_repost, images_count, url, crawl_time, data_hash)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                post_data.get('author'),
                post_data.get('content'),
                post_data.get('publish_time'),
                post_data.get('like_count'),
                post_data.get('comment_count'),
                json.dumps(post_data.get('mentioned_stocks', [])),
                post_data.get('is_repost'),
                post_data.get('images_count'),
                post_data.get('url'),
                datetime.now(),
                data_hash
            ))
            
            await self.connection.commit()
            
        except Exception as e:
            logger.error(f"保存用户动态数据失败: {e}")

    async def save_stock_comment(self, comment_data: Dict[str, Any]):
        """保存股票评论数据"""
        try:
            data_hash = self._generate_data_hash(comment_data)
            
            await self.connection.execute("""
                INSERT OR IGNORE INTO stock_comments 
                (comment_id, stock_code, text, author, author_id, created_at, like_count, 
                 reply_count, sentiment, mentioned_stocks, crawl_time, data_hash)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                comment_data.get('id'),
                comment_data.get('stock_code'),
                comment_data.get('text'),
                comment_data.get('author'),
                comment_data.get('author_id'),
                comment_data.get('created_at'),
                comment_data.get('like_count'),
                comment_data.get('reply_count'),
                comment_data.get('sentiment'),
                json.dumps(comment_data.get('mentioned_stocks', [])),
                datetime.now(),
                data_hash
            ))
            
            await self.connection.commit()
            
        except Exception as e:
            logger.error(f"保存股票评论数据失败: {e}")

    async def update_trending_stocks(self, stock_codes: List[str]):
        """更新热门股票列表"""
        try:
            current_time = datetime.now()
            
            # 插入新的热门股票数据
            for i, stock_code in enumerate(stock_codes):
                await self.connection.execute("""
                    INSERT OR IGNORE INTO trending_stocks (stock_code, rank, update_time)
                    VALUES (?, ?, ?)
                """, (stock_code, i + 1, current_time))
            
            await self.connection.commit()
            
        except Exception as e:
            logger.error(f"更新热门股票列表失败: {e}")

    async def update_stock_comment_stats(self, stock_code: str, comment_count: int):
        """更新股票评论统计"""
        # 这里可以实现更复杂的统计逻辑
        logger.debug(f"股票 {stock_code} 评论统计更新: {comment_count} 条")

    async def cleanup_old_data(self, retention_days: int = 30):
        """清理过期数据"""
        try:
            cutoff_date = datetime.now() - timedelta(days=retention_days)
            
            tables = ['crawl_results', 'hot_stocks', 'hot_topics', 'user_posts', 'stock_comments']
            
            for table in tables:
                await self.connection.execute(f"""
                    DELETE FROM {table} WHERE crawl_time < ?
                """, (cutoff_date,))
            
            await self.connection.commit()
            logger.info(f"已清理 {retention_days} 天前的数据")
            
        except Exception as e:
            logger.error(f"清理过期数据失败: {e}")

    async def get_statistics(self) -> Dict[str, Any]:
        """获取数据统计信息"""
        try:
            stats = {}
            
            # 获取各表的记录数
            tables = ['crawl_results', 'hot_stocks', 'hot_topics', 'user_posts', 'stock_comments', 'trending_stocks']
            
            for table in tables:
                cursor = await self.connection.execute(f"SELECT COUNT(*) FROM {table}")
                count = await cursor.fetchone()
                stats[f"{table}_count"] = count[0] if count else 0
            
            # 获取最近24小时的数据量
            yesterday = datetime.now() - timedelta(days=1)
            cursor = await self.connection.execute("""
                SELECT COUNT(*) FROM crawl_results WHERE crawl_time > ?
            """, (yesterday,))
            recent_count = await cursor.fetchone()
            stats['recent_24h_crawls'] = recent_count[0] if recent_count else 0
            
            return stats
            
        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")
            return {}

    async def close(self):
        """关闭数据库连接"""
        if self.connection:
            await self.connection.close()
            logger.info("数据存储连接已关闭")

#!/usr/bin/env python3
"""
雪球自动爬虫系统演示脚本
展示系统的主要功能和使用方法
"""
import asyncio
import logging
import json
from datetime import datetime, timedelta
from pathlib import Path

from auto_crawler import AutoCrawlerScheduler, AutoCrawlerConfig
from data_storage import DataStorage

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def demo_basic_functionality():
    """演示基本功能"""
    print("=" * 60)
    print("🚀 雪球自动爬虫系统功能演示")
    print("=" * 60)
    
    # 1. 创建配置
    print("\n📋 1. 创建系统配置")
    config = AutoCrawlerConfig(
        homepage_interval=5,  # 演示用较短间隔
        stock_comments_interval=10,
        trending_stocks_interval=3,
        max_concurrent_tasks=2,
        max_comments_per_stock=10,
        max_trending_stocks=20,
        enable_anti_detection=True
    )
    print(f"✓ 配置创建完成")
    print(f"  - 首页爬取间隔: {config.homepage_interval} 分钟")
    print(f"  - 评论爬取间隔: {config.stock_comments_interval} 分钟")
    print(f"  - 最大并发任务: {config.max_concurrent_tasks}")
    
    # 2. 初始化数据存储
    print("\n💾 2. 初始化数据存储")
    data_storage = DataStorage("demo_crawler.db")
    await data_storage.initialize()
    print("✓ 数据存储初始化完成")
    
    # 3. 创建调度器
    print("\n🤖 3. 创建自动爬虫调度器")
    scheduler = AutoCrawlerScheduler(config)
    print("✓ 调度器创建完成")
    
    # 4. 演示手动任务创建
    print("\n📝 4. 创建演示任务")
    from auto_crawler import CrawlTaskInfo
    
    # 创建首页爬取任务
    homepage_task = CrawlTaskInfo(
        task_id="demo_homepage_001",
        task_type="homepage",
        target="https://xueqiu.com",
        priority=8,
        scheduled_time=datetime.now()
    )
    scheduler.task_queue.append(homepage_task)
    print("✓ 首页爬取任务已创建")
    
    # 创建热门股票发现任务
    trending_task = CrawlTaskInfo(
        task_id="demo_trending_001",
        task_type="trending_stocks",
        target="discovery",
        priority=7,
        scheduled_time=datetime.now() + timedelta(seconds=30)
    )
    scheduler.task_queue.append(trending_task)
    print("✓ 热门股票发现任务已创建")
    
    # 创建股票评论任务
    comment_task = CrawlTaskInfo(
        task_id="demo_comments_001",
        task_type="stock_comments",
        target="SH688627",  # 示例股票
        priority=5,
        scheduled_time=datetime.now() + timedelta(seconds=60)
    )
    scheduler.task_queue.append(comment_task)
    print("✓ 股票评论爬取任务已创建")
    
    print(f"\n📊 当前任务队列: {len(scheduler.task_queue)} 个任务")
    
    # 5. 演示任务执行（模拟）
    print("\n⚡ 5. 演示任务执行")
    print("注意：这是演示模式，实际爬取可能需要更长时间")
    
    try:
        # 执行一个任务作为演示
        if scheduler.task_queue:
            demo_task = scheduler.task_queue[0]
            print(f"正在执行任务: {demo_task.task_id} ({demo_task.task_type})")
            
            # 这里只是演示，不实际执行爬取
            demo_task.status = 'completed'
            print("✓ 演示任务执行完成")
    
    except Exception as e:
        print(f"✗ 任务执行失败: {e}")
    
    # 6. 展示监控功能
    print("\n📈 6. 监控功能演示")
    metrics = scheduler.monitor.get_metrics()
    print("当前系统指标:")
    for key, value in metrics.items():
        if key != 'system_status':
            print(f"  - {key}: {value}")
    
    # 7. 展示数据统计
    print("\n📊 7. 数据统计演示")
    stats = await data_storage.get_statistics()
    print("数据库统计:")
    for key, value in stats.items():
        print(f"  - {key}: {value}")
    
    # 8. 清理
    print("\n🧹 8. 清理演示数据")
    await data_storage.close()
    
    # 删除演示数据库
    demo_db = Path("demo_crawler.db")
    if demo_db.exists():
        demo_db.unlink()
        print("✓ 演示数据库已删除")
    
    print("\n🎉 演示完成！")
    print("\n💡 使用提示:")
    print("  - 运行 'python start_auto_crawler.py --create-config' 创建配置文件")
    print("  - 运行 'python start_auto_crawler.py --mode direct' 启动直接模式")
    print("  - 运行 'python start_auto_crawler.py --mode api' 启动API服务器")
    print("  - 查看 AUTO_CRAWLER_GUIDE.md 获取详细使用说明")

async def demo_api_usage():
    """演示API使用"""
    print("\n" + "=" * 60)
    print("🌐 API使用演示")
    print("=" * 60)
    
    print("\n📡 API端点示例:")
    
    api_examples = [
        {
            "method": "POST",
            "endpoint": "/crawler/start",
            "description": "启动爬虫系统",
            "example": """curl -X POST "http://localhost:8000/crawler/start" \\
  -H "Content-Type: application/json" \\
  -d '{
    "homepage_interval": 30,
    "stock_comments_interval": 60,
    "max_concurrent_tasks": 3
  }'"""
        },
        {
            "method": "GET",
            "endpoint": "/crawler/status",
            "description": "获取系统状态",
            "example": 'curl "http://localhost:8000/crawler/status"'
        },
        {
            "method": "POST",
            "endpoint": "/crawler/task/manual",
            "description": "创建手动任务",
            "example": """curl -X POST "http://localhost:8000/crawler/task/manual" \\
  -H "Content-Type: application/json" \\
  -d '{
    "task_type": "stock_comments",
    "target": "SH688627",
    "priority": 8
  }'"""
        },
        {
            "method": "GET",
            "endpoint": "/monitoring/metrics",
            "description": "获取监控指标",
            "example": 'curl "http://localhost:8000/monitoring/metrics"'
        }
    ]
    
    for i, api in enumerate(api_examples, 1):
        print(f"\n{i}. {api['method']} {api['endpoint']}")
        print(f"   描述: {api['description']}")
        print(f"   示例:")
        print(f"   {api['example']}")

def demo_configuration():
    """演示配置选项"""
    print("\n" + "=" * 60)
    print("⚙️ 配置选项演示")
    print("=" * 60)
    
    print("\n📋 主要配置项说明:")
    
    config_options = [
        ("homepage_interval", "首页爬取间隔（分钟）", "30", "建议15-60分钟"),
        ("stock_comments_interval", "个股评论爬取间隔（分钟）", "60", "建议30-120分钟"),
        ("trending_stocks_interval", "热门股票发现间隔（分钟）", "15", "建议10-30分钟"),
        ("max_concurrent_tasks", "最大并发任务数", "3", "建议1-5个"),
        ("max_comments_per_stock", "每股最多爬取评论数", "30", "建议10-100条"),
        ("max_trending_stocks", "最多跟踪热门股票数", "50", "建议20-200只"),
        ("enable_anti_detection", "启用反检测功能", "true", "强烈建议启用"),
        ("data_retention_days", "数据保留天数", "30", "建议7-90天")
    ]
    
    for option, desc, default, recommend in config_options:
        print(f"• {option}")
        print(f"  描述: {desc}")
        print(f"  默认值: {default}")
        print(f"  建议: {recommend}")
        print()
    
    print("📝 配置文件示例 (crawler_config.json):")
    example_config = {
        "homepage_interval": 30,
        "stock_comments_interval": 60,
        "trending_stocks_interval": 15,
        "max_concurrent_tasks": 3,
        "max_comments_per_stock": 30,
        "max_trending_stocks": 50,
        "max_homepage_posts": 200,
        "data_retention_days": 30,
        "max_retries": 3,
        "retry_delay": 300,
        "enable_anti_detection": True,
        "random_delay_range": [5, 15]
    }
    
    print(json.dumps(example_config, indent=2, ensure_ascii=False))

async def main():
    """主演示函数"""
    print("🎭 雪球自动爬虫系统 - 完整演示")
    print("本演示将展示系统的主要功能和使用方法")
    print("\n按 Enter 继续，或 Ctrl+C 退出...")
    
    try:
        input()
    except KeyboardInterrupt:
        print("\n演示已取消")
        return
    
    try:
        # 基本功能演示
        await demo_basic_functionality()
        
        # API使用演示
        demo_api_usage()
        
        # 配置选项演示
        demo_configuration()
        
        print("\n" + "=" * 60)
        print("🎊 演示全部完成！")
        print("=" * 60)
        print("\n📚 更多信息:")
        print("  - 查看 AUTO_CRAWLER_GUIDE.md 获取详细使用指南")
        print("  - 查看 PROJECT_STRUCTURE.md 了解项目结构")
        print("  - 运行 'python start_auto_crawler.py --help' 查看启动选项")
        
    except Exception as e:
        logger.error(f"演示过程中发生错误: {e}")
        print(f"\n❌ 演示失败: {e}")

if __name__ == "__main__":
    asyncio.run(main())

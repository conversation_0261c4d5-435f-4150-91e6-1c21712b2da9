#!/usr/bin/env python3
"""
雪球自动爬虫系统集成测试
验证系统各个组件的功能
"""
import asyncio
import logging
import tempfile
import json
from datetime import datetime, timedelta
from pathlib import Path
import unittest

from auto_crawler import AutoCrawlerScheduler, AutoCrawlerConfig, CrawlTaskInfo
from data_storage import DataStorage
from monitoring import CrawlerMonitor

# 配置测试日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TestAutoCrawlerSystem(unittest.TestCase):
    """自动爬虫系统测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.temp_dir = tempfile.mkdtemp()
        self.test_db_path = Path(self.temp_dir) / "test_crawler.db"
        
        # 创建测试配置
        self.config = AutoCrawlerConfig(
            homepage_interval=1,  # 测试用短间隔
            stock_comments_interval=2,
            trending_stocks_interval=1,
            max_concurrent_tasks=2,
            max_comments_per_stock=5,
            max_trending_stocks=10,
            enable_anti_detection=False  # 测试时禁用反检测
        )
    
    def tearDown(self):
        """测试后清理"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)

class TestDataStorage(TestAutoCrawlerSystem):
    """数据存储测试"""
    
    async def test_database_initialization(self):
        """测试数据库初始化"""
        storage = DataStorage(str(self.test_db_path))
        await storage.initialize()
        
        # 验证数据库文件创建
        self.assertTrue(self.test_db_path.exists())
        
        # 验证表结构
        cursor = await storage.connection.execute(
            "SELECT name FROM sqlite_master WHERE type='table'"
        )
        tables = await cursor.fetchall()
        table_names = [table[0] for table in tables]
        
        expected_tables = [
            'crawl_tasks', 'crawl_results', 'hot_stocks', 
            'hot_topics', 'user_posts', 'stock_comments', 'trending_stocks'
        ]
        
        for table in expected_tables:
            self.assertIn(table, table_names)
        
        await storage.close()
    
    async def test_data_operations(self):
        """测试数据操作"""
        storage = DataStorage(str(self.test_db_path))
        await storage.initialize()
        
        # 测试保存热门股票
        stock_data = {
            'symbol': 'SH688627',
            'name': '精智达',
            'current_price': 100.0,
            'change_percent': 5.2,
            'rank': 1
        }
        await storage.save_hot_stock(stock_data)
        
        # 测试保存评论
        comment_data = {
            'id': 'test_comment_001',
            'stock_code': 'SH688627',
            'text': '这是一条测试评论',
            'author': '测试用户',
            'author_id': 'test_user_001',
            'created_at': '2024-01-15T10:00:00',
            'like_count': 10,
            'reply_count': 2,
            'sentiment': 'positive',
            'mentioned_stocks': ['SH688627']
        }
        await storage.save_stock_comment(comment_data)
        
        # 验证数据保存
        stats = await storage.get_statistics()
        self.assertGreater(stats['hot_stocks_count'], 0)
        self.assertGreater(stats['stock_comments_count'], 0)
        
        await storage.close()

class TestMonitoring(TestAutoCrawlerSystem):
    """监控系统测试"""
    
    def test_monitor_initialization(self):
        """测试监控器初始化"""
        monitor = CrawlerMonitor()
        
        # 验证初始状态
        metrics = monitor.get_metrics()
        self.assertEqual(metrics['total_tasks'], 0)
        self.assertEqual(metrics['successful_tasks'], 0)
        self.assertEqual(metrics['failed_tasks'], 0)
    
    def test_task_recording(self):
        """测试任务记录"""
        monitor = CrawlerMonitor()
        
        # 模拟任务
        task = type('Task', (), {
            'task_id': 'test_task_001',
            'task_type': 'homepage',
            'scheduled_time': datetime.now(),
            'last_error': None
        })()
        
        # 模拟成功结果
        success_result = {
            'success': True,
            'data_type': 'homepage',
            'total_items': {'hot_stocks': 10, 'hot_topics': 5},
            'response_time': 2.5
        }
        
        monitor.record_task_completion(task, success_result)
        
        # 验证指标更新
        metrics = monitor.get_metrics()
        self.assertEqual(metrics['total_tasks'], 1)
        self.assertEqual(metrics['successful_tasks'], 1)
        self.assertEqual(metrics['failed_tasks'], 0)
        self.assertGreater(metrics['success_rate'], 0)

class TestScheduler(TestAutoCrawlerSystem):
    """调度器测试"""
    
    async def test_scheduler_initialization(self):
        """测试调度器初始化"""
        scheduler = AutoCrawlerScheduler(self.config)
        
        # 验证初始状态
        self.assertFalse(scheduler.is_running)
        self.assertEqual(len(scheduler.task_queue), 0)
        self.assertEqual(len(scheduler.running_tasks), 0)
    
    async def test_task_creation(self):
        """测试任务创建"""
        scheduler = AutoCrawlerScheduler(self.config)
        
        # 创建测试任务
        task = CrawlTaskInfo(
            task_id="test_task_001",
            task_type="homepage",
            target="https://xueqiu.com",
            priority=5,
            scheduled_time=datetime.now()
        )
        
        scheduler.task_queue.append(task)
        
        # 验证任务添加
        self.assertEqual(len(scheduler.task_queue), 1)
        self.assertEqual(scheduler.task_queue[0].task_id, "test_task_001")

async def run_integration_tests():
    """运行集成测试"""
    print("🧪 开始雪球自动爬虫系统集成测试")
    print("=" * 50)
    
    # 数据存储测试
    print("\n📊 测试数据存储功能...")
    storage_test = TestDataStorage()
    storage_test.setUp()
    
    try:
        await storage_test.test_database_initialization()
        print("✓ 数据库初始化测试通过")
        
        await storage_test.test_data_operations()
        print("✓ 数据操作测试通过")
        
    except Exception as e:
        print(f"✗ 数据存储测试失败: {e}")
    finally:
        storage_test.tearDown()
    
    # 监控系统测试
    print("\n📈 测试监控功能...")
    monitor_test = TestMonitoring()
    monitor_test.setUp()
    
    try:
        monitor_test.test_monitor_initialization()
        print("✓ 监控器初始化测试通过")
        
        monitor_test.test_task_recording()
        print("✓ 任务记录测试通过")
        
    except Exception as e:
        print(f"✗ 监控系统测试失败: {e}")
    finally:
        monitor_test.tearDown()
    
    # 调度器测试
    print("\n🤖 测试调度器功能...")
    scheduler_test = TestScheduler()
    scheduler_test.setUp()
    
    try:
        await scheduler_test.test_scheduler_initialization()
        print("✓ 调度器初始化测试通过")
        
        await scheduler_test.test_task_creation()
        print("✓ 任务创建测试通过")
        
    except Exception as e:
        print(f"✗ 调度器测试失败: {e}")
    finally:
        scheduler_test.tearDown()
    
    print("\n🎉 集成测试完成！")

async def run_functional_tests():
    """运行功能测试"""
    print("\n🔧 开始功能测试...")
    print("=" * 50)
    
    # 创建临时测试环境
    temp_dir = tempfile.mkdtemp()
    test_db = Path(temp_dir) / "functional_test.db"
    
    try:
        # 测试完整工作流程
        print("\n1. 创建测试配置...")
        config = AutoCrawlerConfig(
            homepage_interval=1,
            max_concurrent_tasks=1,
            max_comments_per_stock=3,
            enable_anti_detection=False
        )
        print("✓ 配置创建成功")
        
        print("\n2. 初始化数据存储...")
        storage = DataStorage(str(test_db))
        await storage.initialize()
        print("✓ 数据存储初始化成功")
        
        print("\n3. 创建调度器...")
        scheduler = AutoCrawlerScheduler(config)
        print("✓ 调度器创建成功")
        
        print("\n4. 测试任务调度...")
        # 添加测试任务
        test_task = CrawlTaskInfo(
            task_id="functional_test_001",
            task_type="homepage",
            target="test",
            priority=5,
            scheduled_time=datetime.now()
        )
        scheduler.task_queue.append(test_task)
        print(f"✓ 测试任务已添加，队列长度: {len(scheduler.task_queue)}")
        
        print("\n5. 测试状态获取...")
        status = scheduler.get_status()
        print(f"✓ 系统状态: 运行中={status['is_running']}, 任务数={status['task_queue_size']}")
        
        print("\n6. 测试监控指标...")
        metrics = scheduler.monitor.get_metrics()
        print(f"✓ 监控指标获取成功，总任务数: {metrics['total_tasks']}")
        
        print("\n7. 清理测试数据...")
        await storage.close()
        print("✓ 数据存储已关闭")
        
    except Exception as e:
        print(f"✗ 功能测试失败: {e}")
        logger.exception("功能测试异常")
    
    finally:
        # 清理临时文件
        import shutil
        shutil.rmtree(temp_dir, ignore_errors=True)
        print("✓ 临时文件已清理")

def test_configuration():
    """测试配置功能"""
    print("\n⚙️ 测试配置功能...")
    print("=" * 50)
    
    try:
        # 测试默认配置
        print("1. 测试默认配置...")
        default_config = AutoCrawlerConfig()
        print(f"✓ 默认配置创建成功")
        print(f"  - 首页间隔: {default_config.homepage_interval} 分钟")
        print(f"  - 最大并发: {default_config.max_concurrent_tasks}")
        
        # 测试自定义配置
        print("\n2. 测试自定义配置...")
        custom_config = AutoCrawlerConfig(
            homepage_interval=15,
            max_concurrent_tasks=3,
            enable_anti_detection=True
        )
        print("✓ 自定义配置创建成功")
        print(f"  - 首页间隔: {custom_config.homepage_interval} 分钟")
        print(f"  - 反检测: {custom_config.enable_anti_detection}")
        
        # 测试配置序列化
        print("\n3. 测试配置序列化...")
        config_dict = {
            'homepage_interval': custom_config.homepage_interval,
            'stock_comments_interval': custom_config.stock_comments_interval,
            'max_concurrent_tasks': custom_config.max_concurrent_tasks,
            'enable_anti_detection': custom_config.enable_anti_detection
        }
        config_json = json.dumps(config_dict, indent=2)
        print("✓ 配置序列化成功")
        print(f"配置JSON长度: {len(config_json)} 字符")
        
    except Exception as e:
        print(f"✗ 配置测试失败: {e}")

async def main():
    """主测试函数"""
    print("🧪 雪球自动爬虫系统 - 完整测试套件")
    print("本测试将验证系统的各个组件功能")
    
    try:
        # 运行集成测试
        await run_integration_tests()
        
        # 运行功能测试
        await run_functional_tests()
        
        # 运行配置测试
        test_configuration()
        
        print("\n" + "=" * 50)
        print("🎊 所有测试完成！")
        print("=" * 50)
        print("\n📋 测试总结:")
        print("  ✓ 数据存储功能正常")
        print("  ✓ 监控系统功能正常")
        print("  ✓ 调度器功能正常")
        print("  ✓ 配置系统功能正常")
        print("\n💡 系统已准备就绪，可以开始使用！")
        
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        logger.exception("测试异常")

if __name__ == "__main__":
    asyncio.run(main())

#!/usr/bin/env python3
"""
雪球自动爬虫系统启动脚本
支持多种启动模式：直接运行、API服务器、守护进程
"""
import asyncio
import argparse
import logging
import signal
import sys
from pathlib import Path
import json
from typing import Dict, Any

from auto_crawler import AutoCrawlerScheduler, AutoCrawlerConfig
from api_server import run_api_server

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('auto_crawler_startup.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class AutoCrawlerLauncher:
    """自动爬虫启动器"""
    
    def __init__(self):
        self.scheduler = None
        self.running = False
        
    def load_config(self, config_file: str = None) -> AutoCrawlerConfig:
        """加载配置文件"""
        default_config = {
            "homepage_interval": 30,
            "stock_comments_interval": 60,
            "trending_stocks_interval": 15,
            "max_concurrent_tasks": 3,
            "max_comments_per_stock": 30,
            "max_trending_stocks": 50,
            "max_homepage_posts": 200,
            "data_retention_days": 30,
            "max_retries": 3,
            "retry_delay": 300,
            "enable_anti_detection": True,
            "random_delay_range": [5, 15]
        }
        
        if config_file and Path(config_file).exists():
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    user_config = json.load(f)
                    default_config.update(user_config)
                logger.info(f"已加载配置文件: {config_file}")
            except Exception as e:
                logger.warning(f"加载配置文件失败，使用默认配置: {e}")
        else:
            logger.info("使用默认配置")
        
        return AutoCrawlerConfig(**default_config)
    
    def save_default_config(self, config_file: str = "crawler_config.json"):
        """保存默认配置文件"""
        default_config = {
            "homepage_interval": 30,
            "stock_comments_interval": 60,
            "trending_stocks_interval": 15,
            "max_concurrent_tasks": 3,
            "max_comments_per_stock": 30,
            "max_trending_stocks": 50,
            "max_homepage_posts": 200,
            "data_retention_days": 30,
            "max_retries": 3,
            "retry_delay": 300,
            "enable_anti_detection": True,
            "random_delay_range": [5, 15]
        }
        
        try:
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(default_config, f, ensure_ascii=False, indent=2)
            logger.info(f"默认配置已保存到: {config_file}")
            print(f"默认配置文件已创建: {config_file}")
            print("您可以编辑此文件来自定义爬虫配置")
        except Exception as e:
            logger.error(f"保存配置文件失败: {e}")
    
    async def run_direct(self, config: AutoCrawlerConfig):
        """直接运行模式"""
        logger.info("启动直接运行模式...")
        
        self.scheduler = AutoCrawlerScheduler(config)
        self.running = True
        
        # 设置信号处理
        def signal_handler(signum, frame):
            logger.info(f"收到信号 {signum}，正在停止系统...")
            self.running = False
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        
        try:
            await self.scheduler.start()
        except Exception as e:
            logger.error(f"系统运行异常: {e}")
            raise
        finally:
            if self.scheduler:
                await self.scheduler.stop()
    
    def run_api_server(self, host: str = "0.0.0.0", port: int = 8000):
        """API服务器模式"""
        logger.info(f"启动API服务器模式: http://{host}:{port}")
        run_api_server(host, port)
    
    async def run_daemon(self, config: AutoCrawlerConfig, pid_file: str = "crawler.pid"):
        """守护进程模式"""
        logger.info("启动守护进程模式...")
        
        # 写入PID文件
        try:
            import os
            with open(pid_file, 'w') as f:
                f.write(str(os.getpid()))
            logger.info(f"PID文件已创建: {pid_file}")
        except Exception as e:
            logger.warning(f"创建PID文件失败: {e}")
        
        try:
            await self.run_direct(config)
        finally:
            # 清理PID文件
            try:
                Path(pid_file).unlink(missing_ok=True)
                logger.info("PID文件已清理")
            except Exception as e:
                logger.warning(f"清理PID文件失败: {e}")
    
    def show_status(self):
        """显示系统状态"""
        print("=== 雪球自动爬虫系统状态 ===")
        
        # 检查数据库文件
        db_file = Path("xueqiu_crawl_data.db")
        if db_file.exists():
            print(f"✓ 数据库文件存在: {db_file} ({db_file.stat().st_size} bytes)")
        else:
            print("✗ 数据库文件不存在")
        
        # 检查日志文件
        log_files = ["auto_crawler.log", "auto_crawler_startup.log", "crawler.log"]
        for log_file in log_files:
            log_path = Path(log_file)
            if log_path.exists():
                print(f"✓ 日志文件: {log_file} ({log_path.stat().st_size} bytes)")
        
        # 检查配置文件
        config_file = Path("crawler_config.json")
        if config_file.exists():
            print(f"✓ 配置文件存在: {config_file}")
        else:
            print("✗ 配置文件不存在（将使用默认配置）")
        
        # 检查PID文件
        pid_file = Path("crawler.pid")
        if pid_file.exists():
            try:
                with open(pid_file, 'r') as f:
                    pid = f.read().strip()
                print(f"✓ 系统可能正在运行 (PID: {pid})")
            except:
                print("? PID文件存在但无法读取")
        else:
            print("✗ 系统未在守护进程模式运行")
        
        print("\n使用 'python start_auto_crawler.py --help' 查看所有选项")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='雪球自动爬虫系统启动器')
    
    # 运行模式
    parser.add_argument('--mode', choices=['direct', 'api', 'daemon'], default='direct',
                       help='运行模式: direct(直接运行), api(API服务器), daemon(守护进程)')
    
    # 配置文件
    parser.add_argument('--config', type=str, default='crawler_config.json',
                       help='配置文件路径')
    
    # API服务器选项
    parser.add_argument('--host', type=str, default='0.0.0.0',
                       help='API服务器主机地址')
    parser.add_argument('--port', type=int, default=8000,
                       help='API服务器端口')
    
    # 守护进程选项
    parser.add_argument('--pid-file', type=str, default='crawler.pid',
                       help='守护进程PID文件路径')
    
    # 工具选项
    parser.add_argument('--create-config', action='store_true',
                       help='创建默认配置文件')
    parser.add_argument('--status', action='store_true',
                       help='显示系统状态')
    
    # 日志级别
    parser.add_argument('--log-level', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       default='INFO', help='日志级别')
    
    args = parser.parse_args()
    
    # 设置日志级别
    logging.getLogger().setLevel(getattr(logging, args.log_level))
    
    launcher = AutoCrawlerLauncher()
    
    try:
        if args.create_config:
            launcher.save_default_config(args.config)
            return
        
        if args.status:
            launcher.show_status()
            return
        
        # 加载配置
        config = launcher.load_config(args.config)
        
        if args.mode == 'direct':
            print("启动直接运行模式...")
            print("按 Ctrl+C 停止系统")
            asyncio.run(launcher.run_direct(config))
            
        elif args.mode == 'api':
            print(f"启动API服务器模式: http://{args.host}:{args.port}")
            print("按 Ctrl+C 停止服务器")
            launcher.run_api_server(args.host, args.port)
            
        elif args.mode == 'daemon':
            print("启动守护进程模式...")
            print(f"PID文件: {args.pid_file}")
            asyncio.run(launcher.run_daemon(config, args.pid_file))
        
    except KeyboardInterrupt:
        print("\n收到中断信号，正在停止...")
        logger.info("用户中断，系统停止")
    except Exception as e:
        print(f"启动失败: {e}")
        logger.error(f"启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
快速测试滚动功能
验证是否能获取更多评论
"""
import asyncio
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def quick_test():
    """快速测试滚动功能"""
    print("🧪 快速测试滚动功能")
    print("=" * 40)
    
    from main_advanced import CrawlerApp

    app = CrawlerApp(enable_anti_detection=True)
    
    # 测试参数
    test_symbol = "SH688775"
    target_count = 20
    strategy = "moderate"
    
    print(f"📊 测试参数:")
    print(f"   股票代码: {test_symbol}")
    print(f"   目标评论数: {target_count}")
    print(f"   滚动策略: {strategy}")
    print()
    
    try:
        start_time = datetime.now()
        print(f"⏰ 开始时间: {start_time.strftime('%H:%M:%S')}")
        
        result = await app.crawl_stock_comments(
            symbol=test_symbol,
            count=target_count,
            scroll_strategy=strategy
        )
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        print(f"⏰ 结束时间: {end_time.strftime('%H:%M:%S')}")
        print(f"⏱️  总耗时: {duration:.1f} 秒")
        print()
        
        if result.get('success'):
            total_comments = result.get('total_comments', 0)
            api_data = result.get('api_format', {})
            comments = api_data.get('list', [])
            
            print(f"✅ 爬取成功!")
            print(f"📈 获取评论数: {total_comments} 条")
            print(f"🎯 目标完成度: {(total_comments/target_count)*100:.1f}%")
            
            if comments:
                # 分析评论质量
                authors = set()
                total_likes = 0
                content_lengths = []
                
                for comment in comments:
                    user = comment.get('user', {})
                    author = user.get('screen_name', '')
                    if author:
                        authors.add(author)
                    
                    total_likes += comment.get('fav_count', 0)
                    content_lengths.append(len(comment.get('text', '')))
                
                print(f"👥 独立用户数: {len(authors)}")
                print(f"👍 总点赞数: {total_likes}")
                print(f"📊 平均点赞: {total_likes/len(comments):.1f}")
                print(f"📝 平均内容长度: {sum(content_lengths)/len(content_lengths):.1f} 字符")
                
                # 显示前5条评论
                print(f"\n💬 评论示例 (前5条):")
                for i, comment in enumerate(comments[:5], 1):
                    author = comment.get('user', {}).get('screen_name', '未知用户')
                    content = comment.get('text', '')
                    likes = comment.get('fav_count', 0)
                    
                    # 截断长内容
                    if len(content) > 60:
                        content = content[:60] + "..."
                    
                    print(f"   {i}. 【{author}】{content} (👍 {likes})")
                
                # 评估滚动效果
                print(f"\n📊 滚动效果评估:")
                if total_comments >= target_count:
                    print(f"🎉 优秀: 达到或超过目标评论数")
                elif total_comments >= target_count * 0.8:
                    print(f"✅ 良好: 达到目标的80%以上")
                elif total_comments >= target_count * 0.6:
                    print(f"⚠️  一般: 达到目标的60%以上")
                else:
                    print(f"❌ 需要改进: 未达到目标的60%")
                
                # 效率分析
                efficiency = total_comments / duration if duration > 0 else 0
                print(f"⚡ 爬取效率: {efficiency:.2f} 评论/秒")
                
                return True
            else:
                print(f"❌ 未获取到评论内容")
                return False
        else:
            error = result.get('error', '未知错误')
            print(f"❌ 爬取失败: {error}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

async def compare_strategies():
    """比较不同滚动策略的效果"""
    print(f"\n🔄 比较滚动策略效果")
    print("=" * 40)
    
    from main_advanced import CrawlerApp

    app = CrawlerApp(enable_anti_detection=True)
    
    strategies = ["conservative", "moderate"]  # 只测试两种策略节省时间
    test_symbol = "SH688521"
    target_count = 15
    
    results = {}
    
    for i, strategy in enumerate(strategies):
        print(f"\n📊 测试策略 {i+1}/{len(strategies)}: {strategy}")
        print("-" * 30)
        
        try:
            start_time = datetime.now()
            result = await app.crawl_stock_comments(
                symbol=test_symbol,
                count=target_count,
                scroll_strategy=strategy
            )
            end_time = datetime.now()
            
            duration = (end_time - start_time).total_seconds()
            
            if result.get('success'):
                comment_count = result.get('total_comments', 0)
                print(f"✅ {strategy}: {comment_count} 条评论 ({duration:.1f}秒)")
                
                results[strategy] = {
                    'count': comment_count,
                    'duration': duration,
                    'efficiency': comment_count / duration if duration > 0 else 0
                }
            else:
                print(f"❌ {strategy}: 失败")
                results[strategy] = {'count': 0, 'duration': duration, 'efficiency': 0}
                
        except Exception as e:
            print(f"❌ {strategy}: 异常 - {e}")
            results[strategy] = {'count': 0, 'duration': 0, 'efficiency': 0}
        
        # 策略间等待
        if i < len(strategies) - 1:
            print("⏳ 等待3秒...")
            await asyncio.sleep(3)
    
    # 结果对比
    print(f"\n📊 策略对比结果:")
    print("-" * 40)
    print(f"{'策略':<12} {'评论数':<8} {'耗时(秒)':<10} {'效率':<10}")
    print("-" * 40)
    
    for strategy, data in results.items():
        print(f"{strategy:<12} {data['count']:<8} {data['duration']:<10.1f} {data['efficiency']:<10.2f}")
    
    # 推荐策略
    if results:
        best_count = max(results.items(), key=lambda x: x[1]['count'])
        best_efficiency = max(results.items(), key=lambda x: x[1]['efficiency'])
        
        print(f"\n🏆 推荐:")
        print(f"   最多评论: {best_count[0]} ({best_count[1]['count']} 条)")
        print(f"   最高效率: {best_efficiency[0]} ({best_efficiency[1]['efficiency']:.2f} 评论/秒)")

async def main():
    """主测试函数"""
    print("🚀 滚动功能快速测试")
    print("=" * 50)
    
    # 测试1: 单次滚动测试
    success = await quick_test()
    
    if success:
        # 测试2: 策略对比 (可选)
        try_compare = input(f"\n❓ 是否进行策略对比测试? (y/N): ").lower().strip()
        if try_compare == 'y':
            await compare_strategies()
    
    print(f"\n🎉 测试完成!")
    print("💡 如果评论数量仍然较少，可能的原因:")
    print("   1. 该股票的评论本身就不多")
    print("   2. 需要登录才能看到更多评论")
    print("   3. 页面结构发生了变化")
    print("   4. 网络延迟导致内容未完全加载")

if __name__ == "__main__":
    asyncio.run(main())

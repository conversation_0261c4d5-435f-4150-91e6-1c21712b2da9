"""
自动爬虫系统API服务器
提供Web接口来管理和监控爬虫系统
"""
import asyncio
import logging
from datetime import datetime
from typing import Dict, Any, Optional
import json
from pathlib import Path

from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.responses import <PERSON><PERSON><PERSON>esponse, FileResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn

from auto_crawler import AutoCrawlerScheduler, AutoCrawlerConfig, CrawlTaskInfo
from data_storage import DataStorage
from monitoring import CrawlerMonitor

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 全局变量
crawler_scheduler: Optional[AutoCrawlerScheduler] = None
app = FastAPI(title="雪球自动爬虫系统API", version="1.0.0")

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Pydantic模型
class CrawlerConfigModel(BaseModel):
    homepage_interval: int = 30
    stock_comments_interval: int = 60
    trending_stocks_interval: int = 15
    max_concurrent_tasks: int = 5
    max_comments_per_stock: int = 50
    max_trending_stocks: int = 100
    max_homepage_posts: int = 200
    enable_anti_detection: bool = True

class ManualTaskModel(BaseModel):
    task_type: str  # 'homepage', 'stock_comments', 'trending_stocks'
    target: str = ""
    priority: int = 5

@app.on_event("startup")
async def startup_event():
    """应用启动事件"""
    logger.info("启动雪球自动爬虫系统API服务器...")

@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭事件"""
    global crawler_scheduler
    if crawler_scheduler:
        await crawler_scheduler.stop()
    logger.info("雪球自动爬虫系统API服务器已关闭")

@app.get("/")
async def root():
    """根路径"""
    return {"message": "雪球自动爬虫系统API", "version": "1.0.0", "status": "running"}

@app.post("/crawler/start")
async def start_crawler(config: CrawlerConfigModel, background_tasks: BackgroundTasks):
    """启动爬虫系统"""
    global crawler_scheduler
    
    try:
        if crawler_scheduler and crawler_scheduler.is_running:
            raise HTTPException(status_code=400, detail="爬虫系统已在运行中")
        
        # 创建配置
        crawler_config = AutoCrawlerConfig(
            homepage_interval=config.homepage_interval,
            stock_comments_interval=config.stock_comments_interval,
            trending_stocks_interval=config.trending_stocks_interval,
            max_concurrent_tasks=config.max_concurrent_tasks,
            max_comments_per_stock=config.max_comments_per_stock,
            max_trending_stocks=config.max_trending_stocks,
            max_homepage_posts=config.max_homepage_posts,
            enable_anti_detection=config.enable_anti_detection
        )
        
        # 创建调度器
        crawler_scheduler = AutoCrawlerScheduler(crawler_config)
        
        # 在后台启动爬虫系统
        background_tasks.add_task(crawler_scheduler.start)
        
        return {"message": "爬虫系统启动成功", "config": config.dict()}
        
    except Exception as e:
        logger.error(f"启动爬虫系统失败: {e}")
        raise HTTPException(status_code=500, detail=f"启动失败: {str(e)}")

@app.post("/crawler/stop")
async def stop_crawler():
    """停止爬虫系统"""
    global crawler_scheduler
    
    try:
        if not crawler_scheduler or not crawler_scheduler.is_running:
            raise HTTPException(status_code=400, detail="爬虫系统未在运行")
        
        await crawler_scheduler.stop()
        crawler_scheduler = None
        
        return {"message": "爬虫系统已停止"}
        
    except Exception as e:
        logger.error(f"停止爬虫系统失败: {e}")
        raise HTTPException(status_code=500, detail=f"停止失败: {str(e)}")

@app.get("/crawler/status")
async def get_crawler_status():
    """获取爬虫系统状态"""
    global crawler_scheduler
    
    if not crawler_scheduler:
        return {"is_running": False, "message": "爬虫系统未初始化"}
    
    try:
        status = crawler_scheduler.get_status()
        return status
        
    except Exception as e:
        logger.error(f"获取系统状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取状态失败: {str(e)}")

@app.get("/crawler/tasks")
async def get_recent_tasks(limit: int = 20):
    """获取最近的任务信息"""
    global crawler_scheduler
    
    if not crawler_scheduler:
        raise HTTPException(status_code=400, detail="爬虫系统未初始化")
    
    try:
        tasks = crawler_scheduler.get_recent_tasks(limit)
        return {"tasks": tasks, "total": len(tasks)}
        
    except Exception as e:
        logger.error(f"获取任务信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取任务失败: {str(e)}")

@app.post("/crawler/task/manual")
async def create_manual_task(task: ManualTaskModel):
    """创建手动任务"""
    global crawler_scheduler
    
    if not crawler_scheduler:
        raise HTTPException(status_code=400, detail="爬虫系统未初始化")
    
    try:
        # 验证任务类型
        valid_types = ['homepage', 'stock_comments', 'trending_stocks']
        if task.task_type not in valid_types:
            raise HTTPException(status_code=400, detail=f"无效的任务类型，支持: {valid_types}")
        
        # 创建任务
        task_info = CrawlTaskInfo(
            task_id=f"manual_{task.task_type}_{int(datetime.now().timestamp())}",
            task_type=task.task_type,
            target=task.target,
            priority=task.priority,
            scheduled_time=datetime.now()
        )
        
        crawler_scheduler.task_queue.append(task_info)
        
        return {
            "message": "手动任务创建成功",
            "task_id": task_info.task_id,
            "task_type": task_info.task_type,
            "target": task_info.target
        }
        
    except Exception as e:
        logger.error(f"创建手动任务失败: {e}")
        raise HTTPException(status_code=500, detail=f"创建任务失败: {str(e)}")

@app.get("/data/statistics")
async def get_data_statistics():
    """获取数据统计信息"""
    try:
        data_storage = DataStorage()
        await data_storage.initialize()
        
        stats = await data_storage.get_statistics()
        await data_storage.close()
        
        return stats
        
    except Exception as e:
        logger.error(f"获取数据统计失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取统计失败: {str(e)}")

@app.get("/monitoring/metrics")
async def get_monitoring_metrics():
    """获取监控指标"""
    global crawler_scheduler
    
    if not crawler_scheduler:
        raise HTTPException(status_code=400, detail="爬虫系统未初始化")
    
    try:
        metrics = crawler_scheduler.monitor.get_metrics()
        error_summary = crawler_scheduler.monitor.get_error_summary()
        task_stats = crawler_scheduler.monitor.get_task_type_stats()
        
        return {
            "metrics": metrics,
            "error_summary": error_summary,
            "task_type_stats": task_stats
        }
        
    except Exception as e:
        logger.error(f"获取监控指标失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取指标失败: {str(e)}")

@app.get("/monitoring/activity")
async def get_recent_activity(limit: int = 50):
    """获取最近活动"""
    global crawler_scheduler
    
    if not crawler_scheduler:
        raise HTTPException(status_code=400, detail="爬虫系统未初始化")
    
    try:
        activity = crawler_scheduler.monitor.get_recent_activity(limit)
        return {"activity": activity, "total": len(activity)}
        
    except Exception as e:
        logger.error(f"获取最近活动失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取活动失败: {str(e)}")

@app.get("/monitoring/export")
async def export_monitoring_data():
    """导出监控数据"""
    global crawler_scheduler
    
    if not crawler_scheduler:
        raise HTTPException(status_code=400, detail="爬虫系统未初始化")
    
    try:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"crawler_metrics_{timestamp}.json"
        filepath = Path(filename)
        
        crawler_scheduler.monitor.export_metrics(str(filepath))
        
        return FileResponse(
            path=str(filepath),
            filename=filename,
            media_type="application/json"
        )
        
    except Exception as e:
        logger.error(f"导出监控数据失败: {e}")
        raise HTTPException(status_code=500, detail=f"导出失败: {str(e)}")

@app.post("/data/cleanup")
async def cleanup_old_data(retention_days: int = 30):
    """清理过期数据"""
    try:
        data_storage = DataStorage()
        await data_storage.initialize()
        
        await data_storage.cleanup_old_data(retention_days)
        await data_storage.close()
        
        return {"message": f"已清理 {retention_days} 天前的数据"}
        
    except Exception as e:
        logger.error(f"清理数据失败: {e}")
        raise HTTPException(status_code=500, detail=f"清理失败: {str(e)}")

@app.get("/config/current")
async def get_current_config():
    """获取当前配置"""
    global crawler_scheduler
    
    if not crawler_scheduler:
        return {"message": "爬虫系统未初始化"}
    
    return {
        "config": {
            "homepage_interval": crawler_scheduler.config.homepage_interval,
            "stock_comments_interval": crawler_scheduler.config.stock_comments_interval,
            "trending_stocks_interval": crawler_scheduler.config.trending_stocks_interval,
            "max_concurrent_tasks": crawler_scheduler.config.max_concurrent_tasks,
            "max_comments_per_stock": crawler_scheduler.config.max_comments_per_stock,
            "max_trending_stocks": crawler_scheduler.config.max_trending_stocks,
            "enable_anti_detection": crawler_scheduler.config.enable_anti_detection
        }
    }

@app.get("/health")
async def health_check():
    """健康检查"""
    global crawler_scheduler
    
    status = {
        "api_server": "healthy",
        "timestamp": datetime.now().isoformat(),
        "crawler_system": "not_initialized"
    }
    
    if crawler_scheduler:
        if crawler_scheduler.is_running:
            status["crawler_system"] = "running"
        else:
            status["crawler_system"] = "stopped"
    
    return status

def run_api_server(host: str = "0.0.0.0", port: int = 8000):
    """运行API服务器"""
    logger.info(f"启动API服务器: http://{host}:{port}")
    uvicorn.run(app, host=host, port=port)

if __name__ == "__main__":
    run_api_server()
